#!/usr/bin/env python3
"""
Ubuntu Terminal GUI Application using PyQt5
A real Ubuntu terminal implementation with command execution capabilities
"""

import sys
import os
import warnings

# Suppress PyQt5 deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, module="PyQt5")
warnings.filterwarnings("ignore", message=".*sipPyTypeDict.*", category=DeprecationWarning)

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QTextEdit, QLineEdit, QSplitter,
                             QLabel, QFrame, QPushButton, QComboBox, QGridLayout,
                             QGroupBox, QFormLayout, QScrollArea)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QProcess
from PyQt5.QtGui import QFont, QTextCursor, QColor, QPalette

from terminal_widget import UbuntuTerminal
from azure_panel import AzureControlPanel


class MainWindow(QMainWindow):
    """Main application window with Ubuntu terminal on the left side"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()


        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Ubuntu Terminal GUI")
        self.setGeometry(100, 100, 1200, 800)
        
        # Set dark theme similar to Ubuntu terminal
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2C2C2C;
                color: #FFFFFF;
            }
            QWidget {
                background-color: #2C2C2C;
                color: #FFFFFF;
            }
        """)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Create left panel for terminal
        left_panel = self.create_terminal_panel()
        splitter.addWidget(left_panel)
        
        # Create right panel (placeholder for future features)
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions (70% terminal, 30% right panel)
        splitter.setSizes([840, 360])
        
    def create_terminal_panel(self):
        """Create the terminal panel on the left side"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setStyleSheet("""
            QFrame {
                background-color: #000000;
                border: 1px solid #404040;
                border-radius: 5px;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Add title label
        title_label = QLabel("Ubuntu Terminal")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-weight: bold;
                font-size: 14px;
                padding: 5px;
                background-color: #404040;
                border-radius: 3px;
            }
        """)
        layout.addWidget(title_label)
        
        # Add terminal widget
        self.terminal = UbuntuTerminal()
        layout.addWidget(self.terminal)
        
        return panel
        
    def create_right_panel(self):
        """Create the right panel with Azure control features"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setStyleSheet("""
            QFrame {
                background-color: #1E1E1E;
                border: 1px solid #404040;
                border-radius: 5px;
            }
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)

        # Create Azure control panel
        self.azure_panel = AzureControlPanel()

        # Connect Azure panel commands to terminal
        self.azure_panel.command_requested.connect(self.execute_azure_command)
        self.azure_panel.progress_command_requested.connect(self.execute_azure_command_with_progress)

        # Connect terminal output to Azure panel for subscription detection
        self.terminal.executor.output_ready.connect(self.azure_panel.handle_terminal_output)

        layout.addWidget(self.azure_panel)

        return panel

    def execute_azure_command(self, command):
        """Execute Azure command in terminal"""
        # Clear current line and add the command
        self.terminal.clear_current_line()
        self.terminal.insert_text_at_cursor(command)

        # Execute the command
        self.terminal.execute_command()

    def execute_azure_command_with_progress(self, command, progress_dialog):
        """Execute Azure command with progress monitoring"""
        # Store the progress dialog for monitoring
        self.current_progress_dialog = progress_dialog

        # Connect to terminal output to monitor progress
        self.terminal.executor.output_ready.connect(self.monitor_command_progress)
        self.terminal.executor.finished.connect(self.command_finished)

        # Execute the command
        self.execute_azure_command(command)

    def monitor_command_progress(self, output):
        """Monitor command output for progress indicators"""
        if hasattr(self, 'current_progress_dialog') and self.current_progress_dialog:
            # Add output to progress dialog
            self.current_progress_dialog.add_output(output.strip())

            # Parse output for specific progress indicators
            output_lower = output.lower()

            # More specific Azure CLI output patterns
            if any(keyword in output_lower for keyword in ["validating", "checking parameters", "validation"]):
                self.current_progress_dialog.update_progress(0, "正在验证参数和配置...")
            elif any(keyword in output_lower for keyword in ["resource group", "checking resource group"]):
                self.current_progress_dialog.update_progress(1, "正在检查资源组状态...")
            elif any(keyword in output_lower for keyword in ["virtual network", "vnet", "creating vnet"]):
                self.current_progress_dialog.update_progress(2, "正在创建或验证虚拟网络...")
            elif any(keyword in output_lower for keyword in ["network security group", "nsg", "security group"]):
                self.current_progress_dialog.update_progress(3, "正在创建网络安全组...")
            elif any(keyword in output_lower for keyword in ["public ip", "publicip", "ip address"]):
                self.current_progress_dialog.update_progress(4, "正在分配公共IP地址...")
            elif any(keyword in output_lower for keyword in ["network interface", "nic", "creating nic"]):
                self.current_progress_dialog.update_progress(5, "正在创建网络接口...")
            elif any(keyword in output_lower for keyword in ["creating vm", "vm create", "virtual machine"]):
                self.current_progress_dialog.update_progress(6, "正在准备虚拟机配置...")
            elif any(keyword in output_lower for keyword in ["vm instance", "creating instance"]):
                self.current_progress_dialog.update_progress(7, "正在创建虚拟机实例...")
            elif any(keyword in output_lower for keyword in ["os disk", "disk", "storage"]):
                self.current_progress_dialog.update_progress(8, "正在配置操作系统磁盘...")
            elif any(keyword in output_lower for keyword in ["image", "downloading", "deploying"]):
                self.current_progress_dialog.update_progress(9, "正在下载和部署镜像...")
            elif any(keyword in output_lower for keyword in ["network config", "configuring network", "security"]):
                self.current_progress_dialog.update_progress(10, "正在配置网络和安全设置...")
            elif any(keyword in output_lower for keyword in ["starting", "booting", "powering on"]):
                self.current_progress_dialog.update_progress(11, "正在启动虚拟机...")
            elif any(keyword in output_lower for keyword in ["succeeded", "completed", "finished"]):
                self.current_progress_dialog.update_progress(12, "正在进行最终配置检查...")

    def command_finished(self, exit_code):
        """Handle command completion"""
        if hasattr(self, 'current_progress_dialog') and self.current_progress_dialog:
            # Stop the progress timer in Azure panel
            self.azure_panel.stop_progress_timer()

            if exit_code == 0:
                self.current_progress_dialog.set_completed()
                self.current_progress_dialog.add_output("✅ 虚拟机创建成功完成！")
            else:
                self.current_progress_dialog.set_error(f"命令执行失败，退出代码: {exit_code}")

            # Disconnect the progress monitoring
            try:
                self.terminal.executor.output_ready.disconnect(self.monitor_command_progress)
                self.terminal.executor.finished.disconnect(self.command_finished)
            except:
                pass  # Ignore if already disconnected

            # Don't set to None immediately, let user close the dialog manually
            # self.current_progress_dialog = None

    def load_azure_account_info(self):
        """Load current Azure account information on startup"""
        # Add welcome message
        self.terminal.append_colored_text("\n=== Azure CLI GUI 启动 ===\n", "#00FF00")
        self.terminal.append_colored_text("正在检查Azure账户认证状态...\n", "#FFFF00")

        # Check account status
        self.check_account_status()

    def check_account_status(self):
        """Check Azure account authentication status"""
        self.terminal.append_colored_text("\n正在检查账户认证状态...\n", "#FFFF00")
        self.terminal.clear_current_line()
        self.terminal.insert_text_at_cursor("az account show")
        self.terminal.execute_command()


def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Ubuntu Terminal GUI")
    app.setApplicationVersion("1.0")
    
    # Create and show main window
    window = MainWindow()
    window.show()
    
    # Start event loop
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
