#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟机创建队列管理对话框
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QLabel, QMessageBox, QTabWidget, QWidget,
                             QProgressBar, QTextEdit, QSplitter, QGroupBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QColor


class VMQueueDialog(QDialog):
    """虚拟机创建队列管理对话框"""
    
    def __init__(self, vm_queue, parent=None):
        super().__init__(parent)
        self.vm_queue = vm_queue
        self.init_ui()

        # 延迟设置回调和刷新数据
        QTimer.singleShot(100, self._delayed_init)

    def _delayed_init(self):
        """延迟初始化"""
        try:
            print("开始队列对话框延迟初始化...")

            # 先设置回调
            self.setup_callbacks()
            print("回调设置完成")

            # 延迟刷新数据
            QTimer.singleShot(500, self._safe_refresh_data)

            # 定时刷新（延迟启动）
            QTimer.singleShot(1000, self._start_refresh_timer)

            print("队列对话框延迟初始化完成")

        except Exception as e:
            print(f"队列对话框延迟初始化失败: {str(e)}")
            self.append_log(f"初始化失败: {str(e)}")

    def _safe_refresh_data(self):
        """安全的数据刷新"""
        try:
            print("开始安全刷新数据...")
            self.refresh_data()
            print("数据刷新完成")
        except Exception as e:
            print(f"安全刷新数据失败: {str(e)}")
            self.append_log(f"数据刷新失败: {str(e)}")

    def _start_refresh_timer(self):
        """启动刷新定时器"""
        try:
            print("启动刷新定时器...")
            self.refresh_timer = QTimer()
            self.refresh_timer.timeout.connect(self._safe_refresh_data)
            self.refresh_timer.start(5000)  # 每5秒刷新一次，减少频率
            print("刷新定时器启动完成")
        except Exception as e:
            print(f"启动刷新定时器失败: {str(e)}")
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("虚拟机创建队列管理")
        self.setModal(False)  # 非模态对话框
        self.resize(1200, 800)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("虚拟机创建队列管理")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 队列状态概览
        status_group = QGroupBox("并行创建队列状态")
        status_layout = QHBoxLayout(status_group)

        self.total_label = QLabel("总任务: 0")
        self.pending_label = QLabel("等待中: 0")
        self.running_label = QLabel("运行中: 0")
        self.completed_label = QLabel("已完成: 0")
        self.failed_label = QLabel("失败: 0")
        self.parallel_label = QLabel("并发数: 40")
        self.active_label = QLabel("活动线程: 0")

        status_layout.addWidget(self.total_label)
        status_layout.addWidget(self.pending_label)
        status_layout.addWidget(self.running_label)
        status_layout.addWidget(self.completed_label)
        status_layout.addWidget(self.failed_label)
        status_layout.addWidget(self.parallel_label)
        status_layout.addWidget(self.active_label)
        status_layout.addStretch()
        
        layout.addWidget(status_group)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 批次管理选项卡
        self.batches_tab = QWidget()
        self.tab_widget.addTab(self.batches_tab, "批次管理")
        self.init_batches_tab()
        
        # 任务详情选项卡
        self.tasks_tab = QWidget()
        self.tab_widget.addTab(self.tasks_tab, "任务详情")
        self.init_tasks_tab()
        
        # 实时日志选项卡
        self.logs_tab = QWidget()
        self.tab_widget.addTab(self.logs_tab, "实时日志")
        self.init_logs_tab()
        
        # 按钮区域
        button_layout = QHBoxLayout()

        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_data)
        button_layout.addWidget(self.refresh_btn)

        self.delete_batch_btn = QPushButton("删除选中批次")
        self.delete_batch_btn.clicked.connect(self.delete_selected_batch)
        self.delete_batch_btn.setEnabled(False)
        button_layout.addWidget(self.delete_batch_btn)

        self.force_delete_batch_btn = QPushButton("强制删除批次")
        self.force_delete_batch_btn.clicked.connect(self.force_delete_selected_batch)
        self.force_delete_batch_btn.setEnabled(False)
        self.force_delete_batch_btn.setStyleSheet("QPushButton { background-color: #ff6b6b; color: white; font-weight: bold; }")
        button_layout.addWidget(self.force_delete_batch_btn)

        self.delete_completed_btn = QPushButton("删除已完成批次")
        self.delete_completed_btn.clicked.connect(self.delete_completed_batches)
        button_layout.addWidget(self.delete_completed_btn)

        self.stop_all_btn = QPushButton("⏹️ 停止所有任务")
        self.stop_all_btn.clicked.connect(self.stop_all_tasks)
        self.stop_all_btn.setStyleSheet("QPushButton { background-color: #fd7e14; color: white; font-weight: bold; }")
        button_layout.addWidget(self.stop_all_btn)

        self.force_clear_all_btn = QPushButton("🔥 强制清空队列")
        self.force_clear_all_btn.clicked.connect(self.force_clear_all_batches)
        self.force_clear_all_btn.setStyleSheet("QPushButton { background-color: #dc3545; color: white; font-weight: bold; }")
        button_layout.addWidget(self.force_clear_all_btn)

        button_layout.addStretch()

        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)
        
    def init_batches_tab(self):
        """初始化批次管理选项卡"""
        layout = QVBoxLayout(self.batches_tab)
        
        # 批次表格
        self.batches_table = QTableWidget()
        self.batches_table.setColumnCount(7)
        self.batches_table.setHorizontalHeaderLabels([
            "批次名称", "创建时间", "总任务", "已完成", "失败", "运行中", "等待中"
        ])
        
        header = self.batches_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.batches_table.setAlternatingRowColors(True)
        self.batches_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.batches_table.itemSelectionChanged.connect(self.on_batch_selection_changed)
        self.batches_table.itemSelectionChanged.connect(self.update_delete_button_state)
        
        layout.addWidget(self.batches_table)
        
        # 批次详情
        self.batch_details = QTextEdit()
        self.batch_details.setMaximumHeight(200)
        self.batch_details.setReadOnly(True)
        self.batch_details.setPlaceholderText("选择批次查看详细信息...")
        layout.addWidget(self.batch_details)
        
    def init_tasks_tab(self):
        """初始化任务详情选项卡"""
        layout = QVBoxLayout(self.tasks_tab)
        
        # 任务表格
        self.tasks_table = QTableWidget()
        self.tasks_table.setColumnCount(6)
        self.tasks_table.setHorizontalHeaderLabels([
            "虚拟机名称", "区域", "状态", "进度", "创建时间", "错误信息"
        ])
        
        header = self.tasks_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.tasks_table.setAlternatingRowColors(True)
        self.tasks_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        layout.addWidget(self.tasks_table)
        
    def init_logs_tab(self):
        """初始化实时日志选项卡"""
        layout = QVBoxLayout(self.logs_tab)
        
        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        self.logs_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.logs_text)
        
        # 清除日志按钮
        clear_btn = QPushButton("清除日志")
        clear_btn.clicked.connect(self.logs_text.clear)
        layout.addWidget(clear_btn)
        
    def setup_callbacks(self):
        """设置队列回调函数"""
        self.vm_queue.on_task_started = self.on_task_started
        self.vm_queue.on_task_progress = self.on_task_progress
        self.vm_queue.on_task_completed = self.on_task_completed
        self.vm_queue.on_task_failed = self.on_task_failed
        self.vm_queue.on_batch_completed = self.on_batch_completed
        self.vm_queue.on_data_changed = self.on_data_changed
        
    def on_task_started(self, task_id, vm_name):
        """任务开始回调"""
        self.append_log(f"🚀 开始创建虚拟机: {vm_name} (任务ID: {task_id[:8]})")
        
    def on_task_progress(self, task_id, progress, message):
        """任务进度回调"""
        # 只记录重要的进度节点
        if progress in [25, 50, 75, 90]:
            self.append_log(f"📊 任务 {task_id[:8]}: {progress}% - {message}")
            
    def on_task_completed(self, task_id, result):
        """任务完成回调"""
        vm_name = result.get('vm_name', 'Unknown')
        public_ip = result.get('public_ip', '无')
        self.append_log(f"✅ 虚拟机创建成功: {vm_name} | 公共IP: {public_ip} (任务ID: {task_id[:8]})")
        
    def on_task_failed(self, task_id, error):
        """任务失败回调"""
        self.append_log(f"❌ 虚拟机创建失败: 任务ID {task_id[:8]} | 错误: {error}")
        
    def on_batch_completed(self, batch_id, completed_count, failed_count):
        """批次完成回调"""
        self.append_log(f"🎯 批次完成: {batch_id[:8]} | 成功: {completed_count} | 失败: {failed_count}")

    def on_data_changed(self, change_type, data_id):
        """数据变更回调"""
        if change_type == 'batch_added':
            self.append_log(f"📦 新批次已添加: {data_id[:8]}...")
            # 延迟刷新数据，确保数据已保存
            QTimer.singleShot(100, self.refresh_data)
        elif change_type == 'batch_deleted':
            self.append_log(f"🗑️ 批次已删除: {data_id[:8]}...")
            QTimer.singleShot(100, self.refresh_data)

    def append_log(self, message):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logs_text.append(f"[{timestamp}] {message}")
        
    def refresh_data(self):
        """刷新数据"""
        try:
            if not self.vm_queue:
                return

            self.refresh_queue_status()
            self.refresh_batches_table()
            self.refresh_tasks_table()

        except Exception as e:
            print(f"刷新数据失败: {str(e)}")
            self.append_log(f"刷新数据失败: {str(e)}")
        
    def refresh_queue_status(self):
        """刷新队列状态"""
        try:
            status = self.vm_queue.get_queue_status()

            self.total_label.setText(f"总任务: {status.get('total_tasks', 0)}")
            self.pending_label.setText(f"等待中: {status.get('pending', 0)}")
            self.running_label.setText(f"运行中: {status.get('running', 0)}")
            self.completed_label.setText(f"已完成: {status.get('completed', 0)}")
            self.failed_label.setText(f"失败: {status.get('failed', 0)}")

            # 设置颜色
            self.pending_label.setStyleSheet("color: orange; font-weight: bold;")
            self.running_label.setStyleSheet("color: blue; font-weight: bold;")
            self.completed_label.setStyleSheet("color: green; font-weight: bold;")
            self.failed_label.setStyleSheet("color: red; font-weight: bold;")
            self.parallel_label.setStyleSheet("color: purple; font-weight: bold;")

            # 更新并行状态信息
            running_count = status.get('running', 0)
            active_count = self.vm_queue.get_active_tasks_count()

            if running_count > 0:
                self.parallel_label.setText(f"并发数: 40 (活跃: {running_count})")
            else:
                self.parallel_label.setText("并发数: 40 (空闲)")

            # 更新活动线程数
            if active_count > 0:
                self.active_label.setText(f"活动线程: {active_count}")
                self.active_label.setStyleSheet("color: #007bff; font-weight: bold;")
            else:
                self.active_label.setText("活动线程: 0")
                self.active_label.setStyleSheet("color: gray; font-weight: bold;")

        except Exception as e:
            print(f"刷新队列状态失败: {str(e)}")
            # 设置默认值
            self.total_label.setText("总任务: 0")
            self.pending_label.setText("等待中: 0")
            self.running_label.setText("运行中: 0")
            self.completed_label.setText("已完成: 0")
            self.failed_label.setText("失败: 0")
        
    def refresh_batches_table(self):
        """刷新批次表格"""
        try:
            print("开始刷新批次表格...")
            batches = self.vm_queue.get_all_batches()
            print(f"获取到 {len(batches)} 个批次")

            self.batches_table.setRowCount(len(batches))

            for row, batch in enumerate(batches):
                try:
                    self.batches_table.setItem(row, 0, QTableWidgetItem(str(batch.get('name', 'Unknown'))))

                    # 安全处理时间格式
                    created_time = batch.get('created_time')
                    if hasattr(created_time, 'strftime'):
                        time_str = created_time.strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        time_str = str(created_time) if created_time else "Unknown"
                    self.batches_table.setItem(row, 1, QTableWidgetItem(time_str))

                    self.batches_table.setItem(row, 2, QTableWidgetItem(str(batch.get('total_tasks', 0))))
                    self.batches_table.setItem(row, 3, QTableWidgetItem(str(batch.get('completed_tasks', 0))))
                    self.batches_table.setItem(row, 4, QTableWidgetItem(str(batch.get('failed_tasks', 0))))
                    self.batches_table.setItem(row, 5, QTableWidgetItem(str(batch.get('running_tasks', 0))))
                    self.batches_table.setItem(row, 6, QTableWidgetItem(str(batch.get('pending_tasks', 0))))

                    # 存储批次ID
                    batch_id = batch.get('batch_id', '')
                    if self.batches_table.item(row, 0):
                        self.batches_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, batch_id)

                except Exception as e:
                    print(f"处理批次 {row} 时出错: {str(e)}")
                    continue

            print("批次表格刷新完成")

        except Exception as e:
            print(f"刷新批次表格失败: {str(e)}")
            self.append_log(f"刷新批次表格失败: {str(e)}")
            
    def refresh_tasks_table(self):
        """刷新任务表格"""
        try:
            print("开始刷新任务表格...")

            # 限制任务数量，避免界面卡顿
            max_tasks = 100
            all_tasks = []

            try:
                batches = self.vm_queue.get_all_batches()
                for batch in batches[:10]:  # 最多显示10个批次
                    try:
                        batch_status = self.vm_queue.get_batch_status(batch['batch_id'])
                        if batch_status and 'tasks' in batch_status:
                            all_tasks.extend(batch_status['tasks'])
                            if len(all_tasks) >= max_tasks:
                                break
                    except Exception as e:
                        print(f"获取批次状态失败: {str(e)}")
                        continue
            except Exception as e:
                print(f"获取批次列表失败: {str(e)}")
                return

            # 限制显示的任务数量
            all_tasks = all_tasks[:max_tasks]
            print(f"准备显示 {len(all_tasks)} 个任务")

            self.tasks_table.setRowCount(len(all_tasks))

            for row, task in enumerate(all_tasks):
                try:
                    # 检查任务是否为None或无效
                    if not task or not isinstance(task, dict):
                        print(f"任务 {row} 为空或无效，跳过")
                        continue

                    vm_name = task.get('vm_name', 'Unknown') or 'Unknown'
                    vm_name = str(vm_name)  # 确保是字符串

                    # 安全获取区域信息
                    location = 'Unknown'
                    try:
                        task_id = task.get('task_id')
                        if task_id and hasattr(self.vm_queue, 'tasks'):
                            task_obj = self.vm_queue.tasks.get(task_id)
                            if task_obj and hasattr(task_obj, 'vm_config') and task_obj.vm_config:
                                location = task_obj.vm_config.get('location', 'Unknown') or 'Unknown'
                    except Exception as e:
                        print(f"获取任务 {row} 区域信息失败: {str(e)}")
                        pass

                    self.tasks_table.setItem(row, 0, QTableWidgetItem(str(vm_name)))
                    self.tasks_table.setItem(row, 1, QTableWidgetItem(str(location)))

                    # 状态项
                    status = task.get('status', 'unknown') or 'unknown'
                    status = str(status)  # 确保是字符串
                    status_item = QTableWidgetItem(status)
                    if status == 'completed':
                        status_item.setBackground(QColor(200, 255, 200))
                    elif status == 'failed':
                        status_item.setBackground(QColor(255, 200, 200))
                    elif status == 'running':
                        status_item.setBackground(QColor(200, 200, 255))
                    else:
                        status_item.setBackground(QColor(255, 255, 200))
                    self.tasks_table.setItem(row, 2, status_item)

                    progress = task.get('progress', 0) or 0
                    try:
                        progress = int(progress) if progress is not None else 0
                    except (ValueError, TypeError):
                        progress = 0
                    self.tasks_table.setItem(row, 3, QTableWidgetItem(f"{progress}%"))

                    # 创建时间
                    created_time = "Unknown"
                    try:
                        task_id = task.get('task_id')
                        if task_id and hasattr(self.vm_queue, 'tasks'):
                            task_obj = self.vm_queue.tasks.get(task_id)
                            if task_obj and hasattr(task_obj, 'created_time') and task_obj.created_time:
                                if hasattr(task_obj.created_time, 'strftime'):
                                    created_time = task_obj.created_time.strftime("%Y-%m-%d %H:%M:%S")
                                else:
                                    created_time = str(task_obj.created_time)
                    except Exception as e:
                        print(f"获取任务 {row} 创建时间失败: {str(e)}")
                        pass
                    self.tasks_table.setItem(row, 4, QTableWidgetItem(str(created_time)))

                    # 错误信息
                    error = task.get('error', '') or ''  # 确保error不为None
                    if isinstance(error, str) and len(error) > 50:
                        error_display = error[:50] + '...'
                    else:
                        error_display = str(error) if error else ''
                    error_item = QTableWidgetItem(error_display)
                    if error:
                        error_item.setToolTip(str(error))
                    self.tasks_table.setItem(row, 5, error_item)

                except Exception as e:
                    print(f"处理任务 {row} 时出错: {str(e)}")
                    continue

            print("任务表格刷新完成")

        except Exception as e:
            print(f"刷新任务表格失败: {str(e)}")
            self.append_log(f"刷新任务表格失败: {str(e)}")
            
    def on_batch_selection_changed(self):
        """批次选择变化处理"""
        selected_rows = self.batches_table.selectionModel().selectedRows()
        if not selected_rows:
            self.batch_details.clear()
            return
            
        row = selected_rows[0].row()
        batch_id = self.batches_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
        
        batch_status = self.vm_queue.get_batch_status(batch_id)
        if batch_status:
            details = f"批次ID: {batch_id}\n"
            details += f"批次名称: {batch_status['name']}\n"
            details += f"创建时间: {batch_status['created_time'].strftime('%Y-%m-%d %H:%M:%S')}\n"
            details += f"总任务数: {batch_status['total_tasks']}\n\n"
            
            details += "任务详情:\n"
            for task in batch_status['tasks']:
                details += f"• {task['vm_name']} - {task['status']}"
                if task['status'] == 'running':
                    details += f" ({task['progress']}%)"
                elif task['status'] == 'failed':
                    details += f" - {task['error'][:50]}{'...' if len(task['error']) > 50 else ''}"
                details += "\n"
                
            self.batch_details.setText(details)
            
    def update_delete_button_state(self):
        """更新删除按钮状态"""
        selected_rows = self.batches_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        self.delete_batch_btn.setEnabled(has_selection)
        self.force_delete_batch_btn.setEnabled(has_selection)

    def delete_selected_batch(self):
        """删除选中的批次"""
        selected_rows = self.batches_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        batch_id = self.batches_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
        batch_name = self.batches_table.item(row, 0).text()

        # 确认删除
        reply = QMessageBox.question(
            self, '确认删除批次',
            f'确定要删除批次 "{batch_name}" 吗？\n\n'
            f'这将删除该批次中的所有任务（包括已完成和失败的任务）。\n'
            f'正在运行的任务无法删除。\n\n'
            f'此操作不可撤销！',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 执行删除
            result = self.vm_queue.delete_batch(batch_id)

            if result['success']:
                QMessageBox.information(self, "删除成功", result['message'])
                self.append_log(f"删除批次成功: {batch_name}")
                self.refresh_data()
            else:
                QMessageBox.warning(self, "删除失败", result['error'])
                self.append_log(f"删除批次失败: {batch_name} - {result['error']}")

    def force_delete_selected_batch(self):
        """强制删除选中的批次（包括正在运行的任务）"""
        selected_rows = self.batches_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        batch_id = self.batches_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
        batch_name = self.batches_table.item(row, 0).text()

        # 获取批次状态以显示详细信息
        batch_status = self.vm_queue.get_batch_status(batch_id)
        running_count = 0
        pending_count = 0
        total_count = 0

        if batch_status and 'tasks' in batch_status:
            total_count = len(batch_status['tasks'])
            for task in batch_status['tasks']:
                if task.get('status') == 'running':
                    running_count += 1
                elif task.get('status') == 'pending':
                    pending_count += 1

        # 强制删除确认对话框
        reply = QMessageBox.question(
            self, '⚠️ 强制删除批次确认',
            f'🚨 您即将强制删除批次 "{batch_name}"！\n\n'
            f'📊 批次状态：\n'
            f'   • 总任务数：{total_count}\n'
            f'   • 正在运行：{running_count}\n'
            f'   • 等待中：{pending_count}\n\n'
            f'⚠️  警告：\n'
            f'   • 正在运行的任务将被强制取消\n'
            f'   • 可能导致Azure资源处于不完整状态\n'
            f'   • 此操作不可撤销！\n\n'
            f'❓ 确定要强制删除吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 二次确认
            final_reply = QMessageBox.warning(
                self, '🔥 最终确认',
                f'最后确认：您真的要强制删除批次 "{batch_name}" 吗？\n\n'
                f'这将立即停止 {running_count} 个正在运行的虚拟机创建任务！\n\n'
                f'点击 "Yes" 执行强制删除\n'
                f'点击 "No" 取消操作',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if final_reply == QMessageBox.StandardButton.Yes:
                # 执行强制删除
                result = self.vm_queue.force_delete_batch(batch_id)

                if result['success']:
                    QMessageBox.information(
                        self, "强制删除成功",
                        f"✅ 批次强制删除完成！\n\n{result['message']}"
                    )
                    self.append_log(f"强制删除批次成功: {batch_name} - 取消了 {result.get('cancelled_tasks', 0)} 个运行中任务")
                    self.refresh_data()
                else:
                    QMessageBox.critical(self, "强制删除失败", f"❌ {result['error']}")
                    self.append_log(f"强制删除批次失败: {batch_name} - {result['error']}")

    def delete_completed_batches(self):
        """删除所有已完成的批次"""
        # 确认删除
        reply = QMessageBox.question(
            self, '确认删除已完成批次',
            '确定要删除所有已完成的批次吗？\n\n'
            '这将删除所有任务都已完成或失败的批次。\n'
            '正在运行或等待中的批次不会被删除。\n\n'
            '此操作不可撤销！',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 执行删除
            result = self.vm_queue.delete_completed_batches()

            if result['success']:
                QMessageBox.information(self, "删除成功", result['message'])
                self.append_log(f"删除已完成批次: {result['message']}")
                self.refresh_data()
            else:
                QMessageBox.warning(self, "删除失败", result['error'])
                self.append_log(f"删除已完成批次失败: {result['error']}")

    def stop_all_tasks(self):
        """停止所有正在运行的任务"""
        # 获取当前状态
        status = self.vm_queue.get_queue_status()
        running_tasks = status.get('running', 0)
        active_threads = self.vm_queue.get_active_tasks_count()

        if running_tasks == 0 and active_threads == 0:
            QMessageBox.information(self, "无任务运行", "当前没有正在运行的任务。")
            return

        # 确认停止
        reply = QMessageBox.question(
            self, '⏹️ 停止所有创建任务',
            f'确定要停止所有正在运行的虚拟机创建任务吗？\n\n'
            f'📊 当前状态：\n'
            f'   • 运行中任务：{running_tasks}\n'
            f'   • 活动线程：{active_threads}\n\n'
            f'⚠️  此操作将：\n'
            f'   • 停止所有正在运行的创建任务\n'
            f'   • 取消线程池中的所有Future任务\n'
            f'   • 重置线程池（包括已删除批次的任务）\n'
            f'   • 正在创建的虚拟机可能处于不完整状态\n\n'
            f'✅ 队列中的等待任务不会被删除，停止后可以重新开始\n\n'
            f'确定要继续吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 执行停止操作
            self.append_log("开始停止所有创建任务...")

            result = self.vm_queue.stop_all_tasks()

            if result['success']:
                QMessageBox.information(
                    self, "停止成功",
                    f"✅ 所有创建任务已停止！\n\n{result['message']}"
                )
                self.append_log(f"停止所有任务成功: 取消 {result.get('cancelled_futures', 0)} 个线程任务，"
                              f"标记 {result.get('cancelled_tasks', 0)} 个数据任务")
                self.refresh_data()
            else:
                QMessageBox.critical(self, "停止失败", f"❌ {result['error']}")
                self.append_log(f"停止所有任务失败: {result['error']}")

    def force_clear_all_batches(self):
        """强制清空所有批次"""
        # 获取当前队列状态
        status = self.vm_queue.get_queue_status()
        total_tasks = status.get('total_tasks', 0)
        running_tasks = status.get('running', 0)
        pending_tasks = status.get('pending', 0)
        total_batches = status.get('batches', 0)

        if total_tasks == 0:
            QMessageBox.information(self, "队列为空", "当前队列中没有任务，无需清空。")
            return

        # 危险操作确认
        reply = QMessageBox.warning(
            self, '🚨 危险操作：强制清空队列',
            f'⚠️  您即将清空整个创建队列！\n\n'
            f'📊 当前队列状态：\n'
            f'   • 总批次数：{total_batches}\n'
            f'   • 总任务数：{total_tasks}\n'
            f'   • 正在运行：{running_tasks}\n'
            f'   • 等待中：{pending_tasks}\n\n'
            f'🔥 此操作将：\n'
            f'   • 删除所有 {total_batches} 个批次\n'
            f'   • 强制取消所有 {running_tasks} 个运行中任务\n'
            f'   • 删除所有 {pending_tasks} 个等待任务\n'
            f'   • 清空整个创建队列\n\n'
            f'⚠️  警告：正在创建的虚拟机可能处于不完整状态！\n'
            f'❌ 此操作不可撤销！\n\n'
            f'确定要继续吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 最终确认
            from PyQt6.QtWidgets import QInputDialog
            text, ok = QInputDialog.getText(
                self, '🔐 最终确认',
                f'为了防止误操作，请输入 "CLEAR ALL" 来确认清空队列：\n\n'
                f'这将强制停止 {running_tasks} 个正在运行的任务！'
            )

            if ok and text.strip() == "CLEAR ALL":
                # 执行强制清空
                result = self.vm_queue.force_clear_all_batches()

                if result['success']:
                    QMessageBox.information(
                        self, "队列清空成功",
                        f"🗑️ 队列强制清空完成！\n\n{result['message']}"
                    )
                    self.append_log(f"强制清空队列: 删除 {result.get('deleted_batches', 0)} 个批次，"
                                  f"取消 {result.get('cancelled_tasks', 0)} 个运行中任务")
                    self.refresh_data()
                else:
                    QMessageBox.critical(self, "清空失败", f"❌ {result['error']}")
                    self.append_log(f"强制清空队列失败: {result['error']}")
            elif ok:
                QMessageBox.warning(self, "确认失败", "输入不正确，操作已取消。")

    def closeEvent(self, event):
        """对话框关闭事件"""
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()
        event.accept()
