#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Azure认证客户端
"""

from azure.identity import ClientSecretCredential, AzureCliCredential
from azure.mgmt.subscription import SubscriptionClient
from azure.mgmt.resource import ResourceManagementClient
from azure.mgmt.compute import ComputeManagementClient
from azure.mgmt.network import NetworkManagementClient
import requests
import subprocess
import json
import re
import webbrowser


class AzureCliAuthClient:
    """Azure CLI认证客户端"""

    def __init__(self, subscription_id=None):
        """
        初始化Azure CLI认证客户端

        Args:
            subscription_id (str): 订阅ID（可选）
        """
        self.subscription_id = subscription_id
        self.credential = None
        self.subscription_client = None
        self.resource_client = None
        self.compute_client = None
        self.network_client = None

    def _get_az_command(self):
        """获取可用的Azure CLI命令"""
        import os

        # 尝试多种方式查找Azure CLI
        az_commands = ['az']

        # 在Windows上，尝试常见的安装路径
        if os.name == 'nt':  # Windows
            possible_paths = [
                r'C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin\az.cmd',
                r'C:\Program Files\Microsoft SDKs\Azure\CLI2\wbin\az.cmd',
                r'C:\Users\<USER>\AppData\Local\Programs\Microsoft\Azure CLI\wbin\az.cmd'.format(os.getenv('USERNAME', '')),
                r'C:\Program Files\Azure CLI\wbin\az.cmd',
                r'C:\Program Files (x86)\Azure CLI\wbin\az.cmd',
            ]

            # 检查这些路径是否存在
            for path in possible_paths:
                if os.path.exists(path):
                    az_commands.append(path)

        # 测试哪个命令可用
        for az_cmd in az_commands:
            try:
                result = subprocess.run(
                    [az_cmd, '--version'],
                    capture_output=True,
                    text=True,
                    timeout=5,
                    shell=True if os.name == 'nt' else False
                )

                if result.returncode == 0:
                    return az_cmd

            except:
                continue

        return None

    @staticmethod
    def check_azure_cli_installed():
        """
        检查Azure CLI是否已安装

        Returns:
            dict: 检查结果
        """
        # 尝试多种方式查找Azure CLI
        az_commands = ['az', 'az.cmd', 'az.exe']

        # 在Windows上，也尝试完整路径
        import os
        if os.name == 'nt':  # Windows
            possible_paths = [
                r'C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin\az.cmd',
                r'C:\Program Files\Microsoft SDKs\Azure\CLI2\wbin\az.cmd',
                r'C:\Users\<USER>\AppData\Local\Programs\Microsoft\Azure CLI\wbin\az.cmd'.format(os.getenv('USERNAME', '')),
            ]
            az_commands.extend(possible_paths)

        for az_cmd in az_commands:
            try:
                result = subprocess.run(
                    [az_cmd, '--version'],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    shell=True if os.name == 'nt' else False  # 在Windows上使用shell
                )

                if result.returncode == 0:
                    # 解析版本信息
                    version_info = result.stdout.split('\n')[0] if result.stdout else "Unknown version"
                    return {
                        'installed': True,
                        'version': version_info,
                        'message': f"Azure CLI已安装: {version_info}",
                        'command': az_cmd
                    }

            except FileNotFoundError:
                continue
            except subprocess.TimeoutExpired:
                continue
            except Exception as e:
                continue

        # 如果所有尝试都失败了
        return {
            'installed': False,
            'error': "未找到Azure CLI",
            'install_guide': {
                'windows': [
                    "方法1: 使用MSI安装包",
                    "1. 访问 https://aka.ms/installazurecliwindows",
                    "2. 下载并运行MSI安装包",
                    "3. 重启命令提示符或PowerShell",
                    "",
                    "方法2: 使用winget (Windows 10/11)",
                    "winget install -e --id Microsoft.AzureCLI",
                    "",
                    "方法3: 使用Chocolatey",
                    "choco install azure-cli",
                    "",
                    "方法4: 使用PowerShell",
                    "Invoke-WebRequest -Uri https://aka.ms/installazurecliwindows -OutFile .\\AzureCLI.msi; Start-Process msiexec.exe -Wait -ArgumentList '/I AzureCLI.msi /quiet'; rm .\\AzureCLI.msi"
                ]
            }
        }

    def start_device_code_flow(self):
        """
        启动设备代码流认证，在后台运行az login命令并获取设备代码

        Returns:
            dict: 包含设备代码和验证URL的信息
        """
        try:
            import os
            import threading
            import queue

            # 获取可用的az命令
            az_cmd = self._get_az_command()
            if not az_cmd:
                return {
                    'success': False,
                    'error': "未找到Azure CLI命令"
                }

            # 创建队列来接收输出
            output_queue = queue.Queue()

            def run_az_login():
                """在线程中运行az login命令"""
                try:
                    process = subprocess.Popen(
                        [az_cmd, 'login', '--use-device-code'],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        shell=True if os.name == 'nt' else False
                    )

                    # 读取stderr输出（设备代码信息通常在stderr中）
                    while True:
                        line = process.stderr.readline()
                        if not line:
                            break
                        output_queue.put(line)

                        # 如果找到设备代码信息，立即返回
                        if 'enter the code' in line:
                            break

                except Exception as e:
                    output_queue.put(f"ERROR: {str(e)}")

            # 启动后台线程
            thread = threading.Thread(target=run_az_login)
            thread.daemon = True
            thread.start()

            # 等待获取设备代码信息
            import time
            timeout = 10  # 10秒超时
            start_time = time.time()

            while time.time() - start_time < timeout:
                try:
                    line = output_queue.get(timeout=1)

                    if line.startswith("ERROR:"):
                        return {
                            'success': False,
                            'error': line[6:]  # 移除"ERROR:"前缀
                        }

                    # 检查是否包含设备代码信息
                    if 'enter the code' in line:
                        # 使用正则表达式提取设备代码和URL
                        code_match = re.search(r'enter the code ([A-Z0-9]+)', line)
                        url_match = re.search(r'open the page (https://[^\s]+)', line)

                        if code_match and url_match:
                            device_code = code_match.group(1)
                            verification_url = url_match.group(1)

                            return {
                                'success': True,
                                'device_code': device_code,
                                'verification_url': verification_url,
                                'message': f"请在浏览器中打开 {verification_url} 并输入代码 {device_code}"
                            }
                        elif code_match:
                            # 如果只找到代码，使用默认URL
                            device_code = code_match.group(1)
                            verification_url = 'https://microsoft.com/devicelogin'

                            return {
                                'success': True,
                                'device_code': device_code,
                                'verification_url': verification_url,
                                'message': f"请在浏览器中打开 {verification_url} 并输入代码 {device_code}"
                            }

                except queue.Empty:
                    continue

            # 超时
            return {
                'success': False,
                'error': "获取设备代码超时，请检查Azure CLI是否正常工作"
            }

        except FileNotFoundError:
            return {
                'success': False,
                'error': "未找到Azure CLI，请确保已安装Azure CLI",
                'install_guide': True
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"启动设备代码流失败: {str(e)}"
            }

    def check_login_status(self):
        """
        检查Azure CLI登录状态

        Returns:
            dict: 登录状态信息
        """
        try:
            import os

            # 获取可用的az命令
            az_cmd = self._get_az_command()
            if not az_cmd:
                return {
                    'success': False,
                    'error': "未找到Azure CLI命令"
                }

            # 检查当前登录状态
            result = subprocess.run(
                [az_cmd, 'account', 'show'],
                capture_output=True,
                text=True,
                timeout=10,
                shell=True if os.name == 'nt' else False
            )

            if result.returncode == 0:
                # 解析账户信息
                account_info = json.loads(result.stdout)
                return {
                    'success': True,
                    'logged_in': True,
                    'account_info': account_info
                }
            else:
                return {
                    'success': True,
                    'logged_in': False,
                    'message': "未登录Azure CLI"
                }

        except Exception as e:
            return {
                'success': False,
                'error': f"检查登录状态失败: {str(e)}"
            }

    def get_subscriptions(self):
        """
        获取可用订阅列表

        Returns:
            list: 订阅列表
        """
        try:
            import os

            # 获取可用的az命令
            az_cmd = self._get_az_command()
            if not az_cmd:
                return []

            result = subprocess.run(
                [az_cmd, 'account', 'list'],
                capture_output=True,
                text=True,
                timeout=15,
                shell=True if os.name == 'nt' else False
            )

            if result.returncode == 0:
                subscriptions = json.loads(result.stdout)
                return [
                    {
                        'subscriptionId': sub['id'],
                        'displayName': sub['name'],
                        'state': sub['state'],
                        'tenantId': sub['tenantId'],
                        'isDefault': sub.get('isDefault', False)
                    }
                    for sub in subscriptions
                ]
            else:
                return []

        except Exception as e:
            print(f"获取订阅列表失败: {str(e)}")
            return []

    def set_subscription(self, subscription_id):
        """
        设置当前订阅

        Args:
            subscription_id (str): 订阅ID

        Returns:
            bool: 设置是否成功
        """
        try:
            import os

            # 获取可用的az命令
            az_cmd = self._get_az_command()
            if not az_cmd:
                return False

            result = subprocess.run(
                [az_cmd, 'account', 'set', '--subscription', subscription_id],
                capture_output=True,
                text=True,
                timeout=10,
                shell=True if os.name == 'nt' else False
            )

            if result.returncode == 0:
                self.subscription_id = subscription_id
                return True
            else:
                return False

        except Exception as e:
            print(f"设置订阅失败: {str(e)}")
            return False

    def authenticate(self):
        """
        使用Azure CLI凭据进行认证

        Returns:
            bool: 认证是否成功
        """
        try:
            # 创建Azure CLI凭据
            self.credential = AzureCliCredential()

            # 创建订阅客户端
            self.subscription_client = SubscriptionClient(self.credential)

            # 如果没有指定订阅ID，获取默认订阅
            if not self.subscription_id:
                subscriptions = self.get_subscriptions()
                if subscriptions:
                    # 查找默认订阅或使用第一个
                    default_sub = next((sub for sub in subscriptions if sub.get('isDefault')), subscriptions[0])
                    self.subscription_id = default_sub['subscriptionId']

            # 测试认证是否有效
            if self.subscription_id:
                subscription = self.subscription_client.subscriptions.get(self.subscription_id)

                if subscription:
                    # 创建其他客户端
                    self.resource_client = ResourceManagementClient(
                        self.credential, self.subscription_id
                    )
                    self.compute_client = ComputeManagementClient(
                        self.credential, self.subscription_id
                    )
                    self.network_client = NetworkManagementClient(
                        self.credential, self.subscription_id
                    )

                    return True

            return False

        except Exception as e:
            print(f"Azure CLI认证失败: {str(e)}")
            return False

    def get_subscription_info(self):
        """
        获取当前订阅信息

        Returns:
            dict: 订阅信息
        """
        try:
            if not self.subscription_client or not self.subscription_id:
                return None

            subscription = self.subscription_client.subscriptions.get(self.subscription_id)

            # 处理state属性
            state_value = 'Unknown'
            if hasattr(subscription, 'state') and subscription.state:
                if hasattr(subscription.state, 'value'):
                    state_value = subscription.state.value
                else:
                    state_value = str(subscription.state)

            # 处理tenant_id属性
            tenant_id = getattr(subscription, 'tenant_id', None)

            return {
                'subscriptionId': subscription.subscription_id,
                'displayName': subscription.display_name,
                'state': state_value,
                'tenantId': tenant_id
            }

        except Exception as e:
            print(f"获取订阅信息失败: {str(e)}")
            return None

    # 为了保持兼容性，Azure CLI认证客户端也需要实现与AuthClient相同的方法
    def get_locations(self):
        """获取可用位置列表"""
        # 使用预定义的Azure区域列表
        predefined_locations = [
            {'name': 'eastus', 'displayName': 'East US', 'regionalDisplayName': 'East US'},
            {'name': 'westus2', 'displayName': 'West US 2', 'regionalDisplayName': 'West US 2'},
            {'name': 'westus3', 'displayName': 'West US 3', 'regionalDisplayName': 'West US 3'},
            {'name': 'australiaeast', 'displayName': 'Australia East', 'regionalDisplayName': 'Australia East'},
            {'name': 'southeastasia', 'displayName': 'Southeast Asia', 'regionalDisplayName': 'Southeast Asia'},
            {'name': 'northeurope', 'displayName': 'North Europe', 'regionalDisplayName': 'North Europe'},
            {'name': 'swedencentral', 'displayName': 'Sweden Central', 'regionalDisplayName': 'Sweden Central'},
            {'name': 'uksouth', 'displayName': 'UK South', 'regionalDisplayName': 'UK South'},
            {'name': 'westeurope', 'displayName': 'West Europe', 'regionalDisplayName': 'West Europe'},
            {'name': 'centralus', 'displayName': 'Central US', 'regionalDisplayName': 'Central US'},
            {'name': 'southafricanorth', 'displayName': 'South Africa North', 'regionalDisplayName': 'South Africa North'},
            {'name': 'centralindia', 'displayName': 'Central India', 'regionalDisplayName': 'Central India'},
            {'name': 'eastasia', 'displayName': 'East Asia', 'regionalDisplayName': 'East Asia'},
            {'name': 'japaneast', 'displayName': 'Japan East', 'regionalDisplayName': 'Japan East'},
            {'name': 'koreacentral', 'displayName': 'Korea Central', 'regionalDisplayName': 'Korea Central'}
        ]

        try:
            # 尝试从Azure API获取位置列表进行验证
            if self.subscription_client:
                try:
                    api_locations = []
                    location_list = self.subscription_client.subscriptions.list_locations(self.subscription_id)

                    for location in location_list:
                        api_locations.append(location.name.lower())

                    # 过滤出API支持的区域
                    filtered_locations = []
                    for loc in predefined_locations:
                        if loc['name'].lower() in api_locations:
                            filtered_locations.append(loc)

                    if filtered_locations:
                        return sorted(filtered_locations, key=lambda x: x['displayName'])

                except Exception as e:
                    print(f"验证区域列表失败，使用预定义列表: {str(e)}")

            # 如果API调用失败，返回预定义列表
            return sorted(predefined_locations, key=lambda x: x['displayName'])

        except Exception as e:
            print(f"获取位置列表失败: {str(e)}")
            return sorted(predefined_locations, key=lambda x: x['displayName'])

    def get_vm_sizes(self, location):
        """获取指定位置的虚拟机规格"""
        try:
            if not self.compute_client:
                return []

            vm_sizes = []
            size_list = self.compute_client.virtual_machine_sizes.list(location)

            for size in size_list:
                vm_sizes.append({
                    'name': size.name,
                    'numberOfCores': size.number_of_cores,
                    'memoryInMB': size.memory_in_mb,
                    'maxDataDiskCount': size.max_data_disk_count,
                    'osDiskSizeInMB': size.os_disk_size_in_mb,
                    'resourceDiskSizeInMB': size.resource_disk_size_in_mb
                })

            return sorted(vm_sizes, key=lambda x: (x['numberOfCores'], x['memoryInMB']))

        except Exception as e:
            print(f"获取虚拟机规格失败: {str(e)}")
            return []

    def get_resource_groups(self):
        """获取资源组列表"""
        try:
            if not self.resource_client:
                return []

            resource_groups = []
            rg_list = self.resource_client.resource_groups.list()

            for rg in rg_list:
                resource_groups.append({
                    'name': rg.name,
                    'location': rg.location,
                    'id': rg.id,
                    'tags': rg.tags or {}
                })

            return sorted(resource_groups, key=lambda x: x['name'])

        except Exception as e:
            print(f"获取资源组列表失败: {str(e)}")
            return []

    def get_virtual_machines(self):
        """获取虚拟机列表"""
        try:
            if not self.compute_client:
                return []

            vms = []
            vm_list = self.compute_client.virtual_machines.list_all()

            for vm in vm_list:
                try:
                    # 获取虚拟机状态
                    resource_group = vm.id.split('/')[4]  # 资源组名称
                    instance_view = self.compute_client.virtual_machines.instance_view(
                        resource_group,
                        vm.name
                    )

                    # 获取电源状态
                    power_state = "Unknown"
                    if instance_view.statuses:
                        for status in instance_view.statuses:
                            if status.code and status.code.startswith('PowerState/'):
                                if hasattr(status, 'display_status'):
                                    if hasattr(status.display_status, 'value'):
                                        power_state = status.display_status.value
                                    else:
                                        power_state = str(status.display_status)
                                elif hasattr(status, 'code'):
                                    power_state = status.code.replace('PowerState/', '')
                                break

                    # 处理虚拟机大小
                    vm_size = "Unknown"
                    if vm.hardware_profile and vm.hardware_profile.vm_size:
                        if hasattr(vm.hardware_profile.vm_size, 'value'):
                            vm_size = vm.hardware_profile.vm_size.value
                        else:
                            vm_size = str(vm.hardware_profile.vm_size)

                    # 处理操作系统类型
                    os_type = "Unknown"
                    if (vm.storage_profile and vm.storage_profile.os_disk and
                        vm.storage_profile.os_disk.os_type):
                        if hasattr(vm.storage_profile.os_disk.os_type, 'value'):
                            os_type = vm.storage_profile.os_disk.os_type.value
                        else:
                            os_type = str(vm.storage_profile.os_disk.os_type)

                    vms.append({
                        'name': vm.name,
                        'resourceGroup': resource_group,
                        'location': vm.location,
                        'vmSize': vm_size,
                        'powerState': power_state,
                        'id': vm.id,
                        'osType': os_type
                    })

                except Exception as vm_e:
                    print(f"获取虚拟机 {vm.name} 详细信息失败: {str(vm_e)}")
                    # 添加基本信息，即使获取详细信息失败
                    vms.append({
                        'name': vm.name,
                        'resourceGroup': vm.id.split('/')[4] if '/' in vm.id else 'Unknown',
                        'location': vm.location if hasattr(vm, 'location') else 'Unknown',
                        'vmSize': 'Unknown',
                        'powerState': 'Unknown',
                        'id': vm.id,
                        'osType': 'Unknown'
                    })

            return sorted(vms, key=lambda x: x['name'])

        except Exception as e:
            print(f"获取虚拟机列表失败: {str(e)}")
            return []

    def get_vm_images(self, location, get_all_images=False):
        """
        获取虚拟机镜像

        Args:
            location (str): 位置名称
            get_all_images (bool): 是否获取所有镜像（默认False，只获取常用镜像）

        Returns:
            list: 镜像列表
        """
        try:
            if not self.compute_client:
                return []

            images = []

            if get_all_images:
                # 获取所有支持的发布者
                publishers = self.get_all_publishers(location)

                for publisher in publishers:
                    try:
                        # 获取该发布者的所有产品
                        offers = self.compute_client.virtual_machine_images.list_offers(
                            location=location,
                            publisher_name=publisher['name']
                        )

                        # 限制每个发布者最多处理前5个产品，避免过多数据
                        for offer in list(offers)[:5]:
                            try:
                                # 获取SKU列表
                                skus = self.compute_client.virtual_machine_images.list_skus(
                                    location=location,
                                    publisher_name=publisher['name'],
                                    offer=offer.name
                                )

                                # 限制每个产品最多处理前3个SKU
                                for sku in list(skus)[:3]:
                                    try:
                                        images.append({
                                            'publisher': publisher['name'],
                                            'offer': offer.name,
                                            'sku': sku.name,
                                            'version': 'latest',
                                            'displayName': f"{publisher['name']} - {offer.name} - {sku.name}"
                                        })
                                    except Exception as sku_e:
                                        print(f"获取SKU {sku.name} 失败: {str(sku_e)}")
                                        continue

                            except Exception as offer_e:
                                print(f"获取产品 {offer.name} 的SKU失败: {str(offer_e)}")
                                continue

                    except Exception as pub_e:
                        print(f"获取发布者 {publisher['name']} 的产品失败: {str(pub_e)}")
                        continue

            else:
                # 定义常用的镜像组合（快速加载）
                popular_images = [
                    {
                        'publisher': 'Canonical',
                        'offer': '0001-com-ubuntu-server-focal',
                        'display_prefix': 'Ubuntu 20.04 LTS',
                        'common_skus': ['20_04-lts-gen2']
                    },
                    {
                        'publisher': 'Canonical',
                        'offer': '0001-com-ubuntu-server-jammy',
                        'display_prefix': 'Ubuntu 22.04 LTS',
                        'common_skus': ['22_04-lts-gen2']
                    },
                    {
                        'publisher': 'MicrosoftWindowsServer',
                        'offer': 'WindowsServer',
                        'display_prefix': 'Windows Server',
                        'common_skus': ['2022-datacenter-g2', '2019-datacenter-gensecond']
                    },
                    {
                        'publisher': 'RedHat',
                        'offer': 'RHEL',
                        'display_prefix': 'Red Hat Enterprise Linux',
                        'common_skus': ['8-lvm-gen2', '9-lvm-gen2']
                    },
                    {
                        'publisher': 'OpenLogic',
                        'offer': 'CentOS',
                        'display_prefix': 'CentOS',
                        'common_skus': ['8_5-gen2', '7_9-gen2']
                    }
                ]

                for image_info in popular_images:
                    try:
                        # 使用预定义的常用SKU，避免大量API调用
                        common_skus = image_info.get('common_skus', [])

                        if common_skus:
                            # 直接使用常用SKU
                            for sku_name in common_skus:
                                try:
                                    images.append({
                                        'publisher': image_info['publisher'],
                                        'offer': image_info['offer'],
                                        'sku': sku_name,
                                        'version': 'latest',
                                        'displayName': f"{image_info['display_prefix']} - {sku_name}"
                                    })
                                except Exception as sku_e:
                                    print(f"添加SKU {sku_name} 失败: {str(sku_e)}")
                                    continue
                        else:
                            # 如果没有预定义SKU，则查询（但限制数量）
                            skus = self.compute_client.virtual_machine_images.list_skus(
                                location=location,
                                publisher_name=image_info['publisher'],
                                offer=image_info['offer']
                            )

                            # 只取前2个SKU以避免列表过长
                            for sku in list(skus)[:2]:
                                try:
                                    images.append({
                                        'publisher': image_info['publisher'],
                                        'offer': image_info['offer'],
                                        'sku': sku.name,
                                        'version': 'latest',
                                        'displayName': f"{image_info['display_prefix']} - {sku.name}"
                                    })
                                except Exception as sku_e:
                                    print(f"获取SKU {sku.name} 失败: {str(sku_e)}")
                                    continue

                    except Exception as offer_e:
                        print(f"获取 {image_info['offer']} 失败: {str(offer_e)}")
                        continue

            # 如果没有获取到任何镜像，返回一些默认选项
            if not images:
                images = [
                    {
                        'publisher': 'Canonical',
                        'offer': '0001-com-ubuntu-server-focal',
                        'sku': '20_04-lts-gen2',
                        'version': 'latest',
                        'displayName': 'Ubuntu 20.04 LTS - 20_04-lts-gen2'
                    },
                    {
                        'publisher': 'MicrosoftWindowsServer',
                        'offer': 'WindowsServer',
                        'sku': '2022-datacenter-g2',
                        'version': 'latest',
                        'displayName': 'Windows Server 2022 Datacenter'
                    }
                ]

            return images

        except Exception as e:
            print(f"获取虚拟机镜像失败: {str(e)}")
            # 返回默认镜像选项
            return [
                {
                    'publisher': 'Canonical',
                    'offer': '0001-com-ubuntu-server-focal',
                    'sku': '20_04-lts-gen2',
                    'version': 'latest',
                    'displayName': 'Ubuntu 20.04 LTS - 20_04-lts-gen2'
                },
                {
                    'publisher': 'MicrosoftWindowsServer',
                    'offer': 'WindowsServer',
                    'sku': '2022-datacenter-g2',
                    'version': 'latest',
                    'displayName': 'Windows Server 2022 Datacenter'
                }
            ]

    def get_all_publishers(self, location):
        """
        获取所有虚拟机镜像发布者

        Args:
            location (str): 位置名称

        Returns:
            list: 发布者列表
        """
        try:
            if not self.compute_client:
                return []

            publishers = []
            publisher_list = self.compute_client.virtual_machine_images.list_publishers(location=location)

            # 定义常用的发布者，优先处理这些
            priority_publishers = [
                'Canonical', 'MicrosoftWindowsServer', 'RedHat', 'OpenLogic',
                'SUSE', 'Oracle', 'Debian', 'MicrosoftSQLServer', 'microsoft-ads'
            ]

            # 先添加优先发布者
            for pub in publisher_list:
                if pub.name in priority_publishers:
                    publishers.append({'name': pub.name, 'priority': True})

            # 再添加其他发布者（限制数量）
            other_count = 0
            for pub in publisher_list:
                if pub.name not in priority_publishers and other_count < 20:
                    publishers.append({'name': pub.name, 'priority': False})
                    other_count += 1

            return publishers

        except Exception as e:
            print(f"获取发布者列表失败: {str(e)}")
            return []

    def get_network_security_groups(self):
        """获取网络安全组列表"""
        try:
            if not self.network_client:
                return []

            nsgs = []
            nsg_list = self.network_client.network_security_groups.list_all()

            for nsg in nsg_list:
                nsgs.append({
                    'name': nsg.name,
                    'resourceGroup': nsg.id.split('/')[4],  # 从资源ID中提取资源组名称
                    'location': nsg.location,
                    'id': nsg.id,
                    'tags': nsg.tags or {}
                })

            return sorted(nsgs, key=lambda x: x['name'])

        except Exception as e:
            print(f"获取网络安全组列表失败: {str(e)}")
            return []

    def get_resource_group_details(self, resource_group_name):
        """获取资源组详细信息"""
        try:
            if not self.resource_client:
                return {}

            # 获取资源组基本信息
            rg = self.resource_client.resource_groups.get(resource_group_name)

            # 获取资源组中的所有资源
            resources = []
            try:
                resource_list = self.resource_client.resources.list_by_resource_group(resource_group_name)
                for resource in resource_list:
                    resources.append({
                        'name': resource.name,
                        'type': resource.type,
                        'location': resource.location,
                        'id': resource.id
                    })
            except Exception as e:
                print(f"获取资源列表失败: {str(e)}")

            # 获取虚拟机列表（包含IP信息）
            vms_with_ips = []
            try:
                if self.compute_client and self.network_client:
                    vm_list = self.compute_client.virtual_machines.list(resource_group_name)
                    for vm in vm_list:
                        vm_info = {
                            'name': vm.name,
                            'size': str(vm.hardware_profile.vm_size) if vm.hardware_profile.vm_size else 'Unknown',
                            'location': vm.location,
                            'public_ips': [],
                            'private_ips': []
                        }

                        # 获取网络接口信息
                        if vm.network_profile and vm.network_profile.network_interfaces:
                            for nic_ref in vm.network_profile.network_interfaces:
                                try:
                                    nic_id = nic_ref.id
                                    nic_name = nic_id.split('/')[-1]
                                    nic = self.network_client.network_interfaces.get(resource_group_name, nic_name)

                                    # 获取私有IP
                                    if nic.ip_configurations:
                                        for ip_config in nic.ip_configurations:
                                            if ip_config.private_ip_address:
                                                vm_info['private_ips'].append(ip_config.private_ip_address)

                                            # 获取公共IP
                                            if ip_config.public_ip_address:
                                                public_ip_id = ip_config.public_ip_address.id
                                                public_ip_name = public_ip_id.split('/')[-1]
                                                public_ip = self.network_client.public_ip_addresses.get(resource_group_name, public_ip_name)
                                                if public_ip.ip_address:
                                                    vm_info['public_ips'].append(public_ip.ip_address)

                                except Exception as nic_e:
                                    print(f"获取网络接口信息失败: {str(nic_e)}")

                        vms_with_ips.append(vm_info)

            except Exception as e:
                print(f"获取虚拟机信息失败: {str(e)}")

            return {
                'name': rg.name,
                'location': rg.location,
                'tags': rg.tags or {},
                'id': rg.id,
                'properties': rg.properties,
                'resources': resources,
                'virtual_machines': vms_with_ips
            }

        except Exception as e:
            print(f"获取资源组详情失败: {str(e)}")
            return {}

    def create_virtual_machine(self, vm_config, progress_callback=None):
        """
        创建虚拟机

        Args:
            vm_config (dict): 虚拟机配置信息
            progress_callback (function): 进度回调函数

        Returns:
            dict: 创建结果
        """
        def update_progress(value, message):
            if progress_callback:
                progress_callback(value, message)

        try:
            if not self.resource_client or not self.compute_client or not self.network_client:
                return {'success': False, 'error': '客户端未初始化'}

            # 导入必要的Azure SDK类
            from azure.mgmt.resource.resources.models import ResourceGroup
            from azure.mgmt.network.models import (
                VirtualNetwork, Subnet, PublicIPAddress, NetworkInterface,
                NetworkSecurityGroup, SecurityRule, IPAllocationMethod,
                NetworkInterfaceIPConfiguration, SubResource
            )
            from azure.mgmt.compute.models import (
                VirtualMachine, HardwareProfile, StorageProfile, OSProfile,
                NetworkProfile, OSDisk, ImageReference, VirtualMachineSizeTypes,
                DiskCreateOptionTypes, NetworkInterfaceReference,
                LinuxConfiguration, WindowsConfiguration
            )

            # 提取配置信息，兼容不同的参数名称
            vm_name = vm_config['vm_name']
            rg_name = vm_config.get('resource_group') or vm_config.get('rg_name')
            location = vm_config['location']
            vm_size = vm_config['vm_size']
            admin_username = vm_config.get('admin_username') or vm_config.get('username')
            admin_password = vm_config.get('admin_password') or vm_config.get('password')
            vm_image = vm_config['vm_image']

            # 验证必需参数
            if not rg_name:
                return {'success': False, 'error': '缺少资源组名称参数 (resource_group 或 rg_name)'}
            if not admin_username:
                return {'success': False, 'error': '缺少管理员用户名参数 (admin_username 或 username)'}
            if not admin_password:
                return {'success': False, 'error': '缺少管理员密码参数 (admin_password 或 password)'}

            update_progress(5, f"🚀 开始创建虚拟机 {vm_name}...")
            update_progress(8, f"📋 配置信息验证完成")

            # 检查资源组是否存在，如果不存在则创建
            update_progress(10, f"🔍 检查资源组 {rg_name}...")
            try:
                self.resource_client.resource_groups.get(rg_name)
                update_progress(15, f"✅ 资源组 {rg_name} 已存在")
            except:
                update_progress(12, f"🏗️ 创建资源组 {rg_name}...")
                rg_params = ResourceGroup(location=location)
                self.resource_client.resource_groups.create_or_update(rg_name, rg_params)
                update_progress(15, f"✅ 资源组 {rg_name} 创建完成")

            # 创建虚拟网络
            update_progress(18, f"🌐 开始创建虚拟网络...")
            vnet_name = f"{vm_name}-vnet"
            update_progress(20, f"🔧 配置虚拟网络参数 (地址空间: 10.0.0.0/16)...")
            vnet_params = VirtualNetwork(
                location=location,
                address_space={'address_prefixes': ['10.0.0.0/16']},
                subnets=[Subnet(
                    name=f"{vm_name}-subnet",
                    address_prefix='10.0.0.0/24'
                )]
            )

            update_progress(22, f"⚡ 提交虚拟网络创建请求...")
            vnet_operation = self.network_client.virtual_networks.begin_create_or_update(
                rg_name, vnet_name, vnet_params
            )
            update_progress(25, f"⏳ 等待虚拟网络创建完成...")
            vnet_result = vnet_operation.result()
            update_progress(30, f"✅ 虚拟网络 {vnet_name} 创建完成")

            # 创建网络安全组
            update_progress(32, f"🛡️ 开始创建网络安全组...")
            nsg_name = f"{vm_name}-nsg"

            # 根据操作系统类型设置安全规则
            update_progress(34, f"🔒 配置安全规则...")
            security_rules = []
            if 'windows' in str(vm_image).lower():
                # Windows - 开放RDP端口
                update_progress(35, f"🪟 配置Windows RDP访问规则 (端口3389)...")
                security_rules.append(SecurityRule(
                    name='AllowRDP',
                    protocol='Tcp',
                    source_port_range='*',
                    destination_port_range='3389',
                    source_address_prefix='*',
                    destination_address_prefix='*',
                    access='Allow',
                    priority=1000,
                    direction='Inbound'
                ))
            else:
                # Linux - 开放SSH端口
                update_progress(35, f"🐧 配置Linux SSH访问规则 (端口22)...")
                security_rules.append(SecurityRule(
                    name='AllowSSH',
                    protocol='Tcp',
                    source_port_range='*',
                    destination_port_range='22',
                    source_address_prefix='*',
                    destination_address_prefix='*',
                    access='Allow',
                    priority=1000,
                    direction='Inbound'
                ))

            update_progress(37, f"⚙️ 创建网络安全组配置...")
            nsg_params = NetworkSecurityGroup(
                location=location,
                security_rules=security_rules
            )

            update_progress(38, f"⚡ 提交网络安全组创建请求...")
            nsg_operation = self.network_client.network_security_groups.begin_create_or_update(
                rg_name, nsg_name, nsg_params
            )
            update_progress(39, f"⏳ 等待网络安全组创建完成...")
            nsg_result = nsg_operation.result()
            update_progress(42, f"✅ 网络安全组 {nsg_name} 创建完成")

            # 创建公共IP地址
            update_progress(44, f"🌍 开始创建公共IP地址...")
            public_ip_name = f"{vm_name}-ip"
            update_progress(46, f"🔧 配置公共IP参数 (Standard SKU, 静态分配)...")

            # 导入PublicIPAddressSku相关类
            from azure.mgmt.network.models import PublicIPAddressSku, PublicIPAddressSkuName

            public_ip_params = PublicIPAddress(
                location=location,
                public_ip_allocation_method=IPAllocationMethod.static,  # Standard SKU需要静态分配
                sku=PublicIPAddressSku(name=PublicIPAddressSkuName.standard),  # 设置为Standard SKU
                dns_settings={'domain_name_label': vm_name.lower()}
            )

            update_progress(48, f"⚡ 提交公共IP创建请求...")
            public_ip_operation = self.network_client.public_ip_addresses.begin_create_or_update(
                rg_name, public_ip_name, public_ip_params
            )
            update_progress(50, f"⏳ 等待公共IP地址分配...")
            public_ip_result = public_ip_operation.result()
            update_progress(55, f"✅ 公共IP地址 {public_ip_name} 创建完成")

            # 创建网络接口
            update_progress(57, f"🔌 开始创建网络接口...")
            nic_name = f"{vm_name}-nic"
            subnet_id = f"{vnet_result.id}/subnets/{vm_name}-subnet"

            update_progress(60, f"🔧 配置网络接口参数...")
            update_progress(62, f"🔗 关联子网: {vm_name}-subnet")
            update_progress(64, f"🌐 关联公共IP: {public_ip_name}")
            update_progress(66, f"🛡️ 关联网络安全组: {nsg_name}")

            nic_params = NetworkInterface(
                location=location,
                ip_configurations=[NetworkInterfaceIPConfiguration(
                    name=f"{vm_name}-ip-config",
                    subnet=SubResource(id=subnet_id),
                    public_ip_address=SubResource(id=public_ip_result.id),
                    private_ip_allocation_method=IPAllocationMethod.dynamic
                )],
                network_security_group=SubResource(id=nsg_result.id)
            )

            update_progress(68, f"⚡ 提交网络接口创建请求...")
            nic_operation = self.network_client.network_interfaces.begin_create_or_update(
                rg_name, nic_name, nic_params
            )
            update_progress(69, f"⏳ 等待网络接口创建完成...")
            nic_result = nic_operation.result()
            update_progress(72, f"✅ 网络接口 {nic_name} 创建完成")

            # 准备虚拟机镜像引用
            update_progress(74, f"💿 准备虚拟机镜像配置...")

            if isinstance(vm_image, dict):
                # 自定义镜像
                update_progress(75, f"🔧 使用自定义镜像: {vm_image.get('publisher', 'Unknown')}")
                image_ref = ImageReference(
                    publisher=vm_image['publisher'],
                    offer=vm_image['offer'],
                    sku=vm_image['sku'],
                    version=vm_image.get('version', 'latest')
                )
            else:
                # 预定义镜像
                if 'ubuntu' in str(vm_image).lower():
                    update_progress(75, f"🐧 使用Ubuntu 20.04 LTS镜像...")
                    image_ref = ImageReference(
                        publisher='Canonical',
                        offer='0001-com-ubuntu-server-focal',
                        sku='20_04-lts-gen2',
                        version='latest'
                    )
                elif 'windows' in str(vm_image).lower():
                    update_progress(75, f"🪟 使用Windows Server 2022镜像...")
                    image_ref = ImageReference(
                        publisher='MicrosoftWindowsServer',
                        offer='WindowsServer',
                        sku='2022-datacenter-g2',
                        version='latest'
                    )
                else:
                    # 默认使用Ubuntu
                    update_progress(75, f"🐧 使用默认Ubuntu 20.04 LTS镜像...")
                    image_ref = ImageReference(
                        publisher='Canonical',
                        offer='0001-com-ubuntu-server-focal',
                        sku='20_04-lts-gen2',
                        version='latest'
                    )

            # 创建OS配置
            update_progress(77, f"⚙️ 配置操作系统参数...")
            update_progress(78, f"👤 设置管理员账户: {admin_username}")
            os_profile = OSProfile(
                computer_name=vm_name,
                admin_username=admin_username,
                admin_password=admin_password
            )

            # 根据操作系统类型配置
            if 'ubuntu' in str(vm_image).lower() or 'linux' in str(vm_image).lower():
                update_progress(79, f"🐧 配置Linux系统设置...")
                os_profile.linux_configuration = LinuxConfiguration(
                    disable_password_authentication=False
                )
            else:
                update_progress(79, f"🪟 配置Windows系统设置...")
                os_profile.windows_configuration = WindowsConfiguration(
                    enable_automatic_updates=True
                )

            # 创建虚拟机参数
            update_progress(80, f"🖥️ 组装虚拟机配置...")
            update_progress(81, f"💾 配置硬件规格: {vm_size}")
            update_progress(82, f"💿 配置存储: {vm_config.get('disk_size', 64)}GB OS磁盘")
            vm_params = VirtualMachine(
                location=location,
                hardware_profile=HardwareProfile(vm_size=vm_size),
                storage_profile=StorageProfile(
                    image_reference=image_ref,
                    os_disk=OSDisk(
                        name=f"{vm_name}-os-disk",
                        create_option=DiskCreateOptionTypes.from_image,
                        disk_size_gb=vm_config.get('disk_size', 64)
                    )
                ),
                os_profile=os_profile,
                network_profile=NetworkProfile(
                    network_interfaces=[
                        NetworkInterfaceReference(id=nic_result.id)
                    ]
                )
            )

            # 开始创建虚拟机
            update_progress(85, f"🚀 提交虚拟机创建请求...")
            update_progress(86, f"⚡ 正在创建虚拟机 {vm_name}...")
            vm_operation = self.compute_client.virtual_machines.begin_create_or_update(
                rg_name, vm_name, vm_params
            )

            # 等待虚拟机创建完成
            update_progress(88, f"⏳ 等待虚拟机 {vm_name} 创建完成...")
            update_progress(90, f"🔄 Azure正在分配计算资源...")
            vm_result = vm_operation.result()

            update_progress(95, f"✅ 虚拟机 {vm_name} 创建完成！")
            update_progress(96, f"🌐 正在获取网络信息...")

            # 获取公共IP地址
            final_public_ip = None
            if public_ip_result:
                try:
                    refreshed_ip = self.network_client.public_ip_addresses.get(rg_name, public_ip_result.name)
                    final_public_ip = refreshed_ip.ip_address
                    if final_public_ip:
                        update_progress(98, f"🌍 公共IP地址: {final_public_ip}")
                    else:
                        update_progress(98, f"🌍 公共IP地址正在分配中...")
                        final_public_ip = "正在分配中"
                except:
                    final_public_ip = "正在分配中"
                    update_progress(98, f"🌍 公共IP地址正在分配中...")

            update_progress(100, f"🎉 虚拟机 {vm_name} 创建完成！")

            return {
                'success': True,
                'vm_name': vm_name,
                'resource_group': rg_name,
                'location': location,
                'vm_size': vm_size,
                'public_ip': final_public_ip,
                'private_ip': '********',  # 默认私有IP
                'admin_username': admin_username,
                'vm_id': vm_result.id,
                'message': f"虚拟机 {vm_name} 创建成功"
            }

        except Exception as e:
            error_msg = f"创建虚拟机失败: {str(e)}"
            update_progress(0, error_msg)
            return {'success': False, 'error': error_msg}

    def delete_resource_group(self, resource_group_name, progress_callback=None):
        """
        删除资源组及其所有资源

        Args:
            resource_group_name (str): 资源组名称
            progress_callback (function): 进度回调函数

        Returns:
            dict: 删除结果
        """
        def update_progress(value, message):
            if progress_callback:
                progress_callback(value, message)

        try:
            if not self.resource_client:
                return {'success': False, 'error': '资源客户端未初始化'}

            update_progress(10, f"开始删除资源组 {resource_group_name}...")

            # 检查资源组是否存在
            try:
                rg = self.resource_client.resource_groups.get(resource_group_name)
                update_progress(20, f"找到资源组 {resource_group_name}")
            except Exception as e:
                if "ResourceGroupNotFound" in str(e):
                    return {
                        'success': True,
                        'message': f"资源组 {resource_group_name} 不存在，无需删除"
                    }
                else:
                    return {'success': False, 'error': f"检查资源组失败: {str(e)}"}

            # 获取资源组中的资源列表
            update_progress(30, f"正在获取资源组 {resource_group_name} 中的资源...")
            try:
                resources = list(self.resource_client.resources.list_by_resource_group(resource_group_name))
                resource_count = len(resources)
                update_progress(40, f"找到 {resource_count} 个资源需要删除")
            except Exception as e:
                update_progress(40, f"获取资源列表失败，继续删除资源组: {str(e)}")

            # 开始删除资源组（这会删除其中的所有资源）
            update_progress(50, f"正在删除资源组 {resource_group_name} 及其所有资源...")

            delete_operation = self.resource_client.resource_groups.begin_delete(resource_group_name)

            # 监控删除进度
            progress = 50
            while not delete_operation.done():
                progress = min(progress + 5, 95)
                if progress <= 95:
                    update_progress(progress, "正在删除资源组...")

                # 等待一段时间再检查
                import time
                time.sleep(2)

            # 等待操作完全完成
            delete_operation.result()

            update_progress(100, f"资源组 {resource_group_name} 删除完成")

            return {
                'success': True,
                'message': f"资源组 {resource_group_name} 及其所有资源已成功删除"
            }

        except Exception as e:
            error_msg = f"删除资源组失败: {str(e)}"
            update_progress(0, error_msg)
            return {'success': False, 'error': error_msg}

    def get_vm_sizes(self, location):
        """
        获取指定位置的虚拟机规格

        Args:
            location (str): 位置名称

        Returns:
            list: 虚拟机规格列表
        """
        try:
            if not self.compute_client:
                return []

            vm_sizes = []
            size_list = self.compute_client.virtual_machine_sizes.list(location)

            for size in size_list:
                vm_sizes.append({
                    'name': size.name,
                    'numberOfCores': size.number_of_cores,
                    'memoryInMB': size.memory_in_mb,
                    'maxDataDiskCount': size.max_data_disk_count,
                    'osDiskSizeInMB': size.os_disk_size_in_mb,
                    'resourceDiskSizeInMB': size.resource_disk_size_in_mb
                })

            return sorted(vm_sizes, key=lambda x: (x['numberOfCores'], x['memoryInMB']))

        except Exception as e:
            print(f"获取虚拟机规格失败: {str(e)}")
            return []

    def get_network_security_group_rules(self, resource_group_name, nsg_name):
        """
        获取网络安全组规则

        Args:
            resource_group_name (str): 资源组名称
            nsg_name (str): 网络安全组名称

        Returns:
            list: 安全规则列表
        """
        try:
            if not self.network_client:
                return []

            nsg = self.network_client.network_security_groups.get(resource_group_name, nsg_name)

            rules = []
            if nsg.security_rules:
                for rule in nsg.security_rules:
                    rules.append({
                        'name': rule.name,
                        'priority': rule.priority,
                        'direction': rule.direction,
                        'access': rule.access,
                        'protocol': rule.protocol,
                        'sourcePortRange': rule.source_port_range,
                        'destinationPortRange': rule.destination_port_range,
                        'sourceAddressPrefix': rule.source_address_prefix,
                        'destinationAddressPrefix': rule.destination_address_prefix,
                        'description': rule.description or ''
                    })

            return sorted(rules, key=lambda x: x['priority'])

        except Exception as e:
            print(f"获取网络安全组规则失败: {str(e)}")
            return []


class AuthClient:
    """Azure认证客户端"""
    
    def __init__(self, auth_info):
        """
        初始化认证客户端
        
        Args:
            auth_info (dict): 认证信息，包含subscription_id, appId, password, tenant
        """
        self.subscription_id = auth_info['subscription_id']
        self.client_id = auth_info['appId']
        self.client_secret = auth_info['password']
        self.tenant_id = auth_info['tenant']
        
        self.credential = None
        self.subscription_client = None
        self.resource_client = None
        self.compute_client = None
        self.network_client = None
        
    def authenticate(self):
        """
        执行认证
        
        Returns:
            bool: 认证是否成功
        """
        try:
            # 创建认证凭据
            self.credential = ClientSecretCredential(
                tenant_id=self.tenant_id,
                client_id=self.client_id,
                client_secret=self.client_secret
            )
            
            # 创建订阅客户端
            self.subscription_client = SubscriptionClient(self.credential)
            
            # 测试认证是否有效
            subscription = self.subscription_client.subscriptions.get(self.subscription_id)
            
            if subscription:
                # 创建其他客户端
                self.resource_client = ResourceManagementClient(
                    self.credential, self.subscription_id
                )
                self.compute_client = ComputeManagementClient(
                    self.credential, self.subscription_id
                )
                self.network_client = NetworkManagementClient(
                    self.credential, self.subscription_id
                )
                
                return True
            else:
                return False
                
        except Exception as e:
            print(f"认证失败: {str(e)}")
            return False
            
    def get_subscription_info(self):
        """
        获取订阅信息

        Returns:
            dict: 订阅信息
        """
        try:
            if not self.subscription_client:
                return None

            subscription = self.subscription_client.subscriptions.get(self.subscription_id)

            # 处理state属性，可能是字符串或枚举
            state_value = 'Unknown'
            if hasattr(subscription, 'state') and subscription.state:
                if hasattr(subscription.state, 'value'):
                    state_value = subscription.state.value
                else:
                    state_value = str(subscription.state)

            # 处理tenant_id属性，可能不存在
            tenant_id = getattr(subscription, 'tenant_id', None) or self.tenant_id

            return {
                'subscriptionId': subscription.subscription_id,
                'displayName': subscription.display_name,
                'state': state_value,
                'tenantId': tenant_id
            }

        except Exception as e:
            print(f"获取订阅信息失败: {str(e)}")
            return None
            
    def get_locations(self):
        """
        获取可用位置列表

        Returns:
            list: 位置列表
        """
        # 使用预定义的Azure区域列表（已删除指定区域）
        predefined_locations = [
            {'name': 'eastus', 'displayName': 'East US', 'regionalDisplayName': 'East US'},
            {'name': 'westus2', 'displayName': 'West US 2', 'regionalDisplayName': 'West US 2'},
            {'name': 'westus3', 'displayName': 'West US 3', 'regionalDisplayName': 'West US 3'},
            {'name': 'australiaeast', 'displayName': 'Australia East', 'regionalDisplayName': 'Australia East'},
            {'name': 'southeastasia', 'displayName': 'Southeast Asia', 'regionalDisplayName': 'Southeast Asia'},
            {'name': 'northeurope', 'displayName': 'North Europe', 'regionalDisplayName': 'North Europe'},
            {'name': 'swedencentral', 'displayName': 'Sweden Central', 'regionalDisplayName': 'Sweden Central'},
            {'name': 'uksouth', 'displayName': 'UK South', 'regionalDisplayName': 'UK South'},
            {'name': 'westeurope', 'displayName': 'West Europe', 'regionalDisplayName': 'West Europe'},
            {'name': 'centralus', 'displayName': 'Central US', 'regionalDisplayName': 'Central US'},
            {'name': 'southafricanorth', 'displayName': 'South Africa North', 'regionalDisplayName': 'South Africa North'},
            {'name': 'centralindia', 'displayName': 'Central India', 'regionalDisplayName': 'Central India'},
            {'name': 'eastasia', 'displayName': 'East Asia', 'regionalDisplayName': 'East Asia'},
            {'name': 'indonesiacentral', 'displayName': 'Indonesia Central', 'regionalDisplayName': 'Indonesia Central'},
            {'name': 'japaneast', 'displayName': 'Japan East', 'regionalDisplayName': 'Japan East'},
            {'name': 'koreacentral', 'displayName': 'Korea Central', 'regionalDisplayName': 'Korea Central'},
            {'name': 'malaysiawest', 'displayName': 'Malaysia West', 'regionalDisplayName': 'Malaysia West'},
            {'name': 'newzealandnorth', 'displayName': 'New Zealand North', 'regionalDisplayName': 'New Zealand North'},
            {'name': 'canadacentral', 'displayName': 'Canada Central', 'regionalDisplayName': 'Canada Central'},
            {'name': 'austriaeast', 'displayName': 'Austria East', 'regionalDisplayName': 'Austria East'},
            {'name': 'francecentral', 'displayName': 'France Central', 'regionalDisplayName': 'France Central'},
            {'name': 'italynorth', 'displayName': 'Italy North', 'regionalDisplayName': 'Italy North'},
            {'name': 'norwayeast', 'displayName': 'Norway East', 'regionalDisplayName': 'Norway East'},
            {'name': 'polandcentral', 'displayName': 'Poland Central', 'regionalDisplayName': 'Poland Central'},
            {'name': 'spaincentral', 'displayName': 'Spain Central', 'regionalDisplayName': 'Spain Central'},
            {'name': 'switzerlandnorth', 'displayName': 'Switzerland North', 'regionalDisplayName': 'Switzerland North'},
            {'name': 'mexicocentral', 'displayName': 'Mexico Central', 'regionalDisplayName': 'Mexico Central'},
            {'name': 'chilecentral', 'displayName': 'Chile Central', 'regionalDisplayName': 'Chile Central'},
            {'name': 'israelcentral', 'displayName': 'Israel Central', 'regionalDisplayName': 'Israel Central'},
            {'name': 'eastus2', 'displayName': 'East US 2', 'regionalDisplayName': 'East US 2'},
            {'name': 'northcentralus', 'displayName': 'North Central US', 'regionalDisplayName': 'North Central US'},
            {'name': 'westus', 'displayName': 'West US', 'regionalDisplayName': 'West US'},
            {'name': 'westcentralus', 'displayName': 'West Central US', 'regionalDisplayName': 'West Central US'},
            {'name': 'australiacentral', 'displayName': 'Australia Central', 'regionalDisplayName': 'Australia Central'},
            {'name': 'australiasoutheast', 'displayName': 'Australia Southeast', 'regionalDisplayName': 'Australia Southeast'},
            {'name': 'koreasouth', 'displayName': 'Korea South', 'regionalDisplayName': 'Korea South'},
            {'name': 'southindia', 'displayName': 'South India', 'regionalDisplayName': 'South India'},
            {'name': 'ukwest', 'displayName': 'UK West', 'regionalDisplayName': 'UK West'},
            {'name': 'brazilsoutheast', 'displayName': 'Brazil Southeast', 'regionalDisplayName': 'Brazil Southeast'}
        ]

        try:
            # 尝试从Azure API获取位置列表进行验证
            if self.subscription_client:
                try:
                    api_locations = []
                    location_list = self.subscription_client.subscriptions.list_locations(self.subscription_id)

                    for location in location_list:
                        api_locations.append(location.name.lower())

                    # 过滤出API支持的区域
                    filtered_locations = []
                    for loc in predefined_locations:
                        if loc['name'].lower() in api_locations:
                            filtered_locations.append(loc)

                    if filtered_locations:
                        print(f"从预定义列表中找到 {len(filtered_locations)} 个可用区域")
                        return sorted(filtered_locations, key=lambda x: x['displayName'])

                except Exception as e:
                    print(f"验证区域列表失败，使用完整预定义列表: {str(e)}")

            # 如果API调用失败或没有匹配的区域，返回完整的预定义列表
            print(f"使用完整的预定义区域列表，共 {len(predefined_locations)} 个区域")
            return sorted(predefined_locations, key=lambda x: x['displayName'])

        except Exception as e:
            print(f"获取位置列表失败，使用预定义列表: {str(e)}")
            return sorted(predefined_locations, key=lambda x: x['displayName'])
            
    def get_vm_sizes(self, location):
        """
        获取指定位置的虚拟机规格
        
        Args:
            location (str): 位置名称
            
        Returns:
            list: 虚拟机规格列表
        """
        try:
            if not self.compute_client:
                return []
                
            vm_sizes = []
            size_list = self.compute_client.virtual_machine_sizes.list(location)
            
            for size in size_list:
                vm_sizes.append({
                    'name': size.name,
                    'numberOfCores': size.number_of_cores,
                    'memoryInMB': size.memory_in_mb,
                    'maxDataDiskCount': size.max_data_disk_count,
                    'osDiskSizeInMB': size.os_disk_size_in_mb,
                    'resourceDiskSizeInMB': size.resource_disk_size_in_mb
                })
                
            return sorted(vm_sizes, key=lambda x: (x['numberOfCores'], x['memoryInMB']))
            
        except Exception as e:
            print(f"获取虚拟机规格失败: {str(e)}")
            return []
            
    def get_vm_images(self, location, get_all_images=False):
        """
        获取虚拟机镜像

        Args:
            location (str): 位置名称
            get_all_images (bool): 是否获取所有镜像（默认False，只获取常用镜像）

        Returns:
            list: 镜像列表
        """
        try:
            if not self.compute_client:
                return []

            images = []

            if get_all_images:
                # 获取所有支持的发布者
                publishers = self.get_all_publishers(location)

                for publisher in publishers:
                    try:
                        # 获取该发布者的所有产品
                        offers = self.compute_client.virtual_machine_images.list_offers(
                            location=location,
                            publisher_name=publisher['name']
                        )

                        # 限制每个发布者最多处理前5个产品，避免过多数据
                        for offer in list(offers)[:5]:
                            try:
                                # 获取SKU列表
                                skus = self.compute_client.virtual_machine_images.list_skus(
                                    location=location,
                                    publisher_name=publisher['name'],
                                    offer=offer.name
                                )

                                # 限制每个产品最多处理前3个SKU
                                for sku in list(skus)[:3]:
                                    try:
                                        images.append({
                                            'publisher': publisher['name'],
                                            'offer': offer.name,
                                            'sku': sku.name,
                                            'version': 'latest',
                                            'displayName': f"{publisher['name']} - {offer.name} - {sku.name}"
                                        })
                                    except Exception as sku_e:
                                        print(f"获取SKU {sku.name} 失败: {str(sku_e)}")
                                        continue

                            except Exception as offer_e:
                                print(f"获取产品 {offer.name} 的SKU失败: {str(offer_e)}")
                                continue

                    except Exception as pub_e:
                        print(f"获取发布者 {publisher['name']} 的产品失败: {str(pub_e)}")
                        continue

            else:
                # 定义常用的镜像组合（快速加载）
                popular_images = [
                    {
                        'publisher': 'Canonical',
                        'offer': '0001-com-ubuntu-server-focal',
                        'display_prefix': 'Ubuntu 20.04 LTS',
                        'common_skus': ['20_04-lts-gen2']
                    },
                    {
                        'publisher': 'Canonical',
                        'offer': '0001-com-ubuntu-server-jammy',
                        'display_prefix': 'Ubuntu 22.04 LTS',
                        'common_skus': ['22_04-lts-gen2']
                    },
                    {
                        'publisher': 'MicrosoftWindowsServer',
                        'offer': 'WindowsServer',
                        'display_prefix': 'Windows Server',
                        'common_skus': ['2022-datacenter-g2', '2019-datacenter-gensecond']
                    },
                    {
                        'publisher': 'RedHat',
                        'offer': 'RHEL',
                        'display_prefix': 'Red Hat Enterprise Linux',
                        'common_skus': ['8-lvm-gen2', '9-lvm-gen2']
                    },
                    {
                        'publisher': 'OpenLogic',
                        'offer': 'CentOS',
                        'display_prefix': 'CentOS',
                        'common_skus': ['8_5-gen2', '7_9-gen2']
                    }
                ]

            for image_info in popular_images:
                try:
                    # 使用预定义的常用SKU，避免大量API调用
                    common_skus = image_info.get('common_skus', [])

                    if common_skus:
                        # 直接使用常用SKU
                        for sku_name in common_skus:
                            try:
                                images.append({
                                    'publisher': image_info['publisher'],
                                    'offer': image_info['offer'],
                                    'sku': sku_name,
                                    'version': 'latest',
                                    'displayName': f"{image_info['display_prefix']} - {sku_name}"
                                })
                            except Exception as sku_e:
                                print(f"添加SKU {sku_name} 失败: {str(sku_e)}")
                                continue
                    else:
                        # 如果没有预定义SKU，则查询（但限制数量）
                        skus = self.compute_client.virtual_machine_images.list_skus(
                            location=location,
                            publisher_name=image_info['publisher'],
                            offer=image_info['offer']
                        )

                        # 只取前2个SKU以避免列表过长
                        for sku in list(skus)[:2]:
                            try:
                                images.append({
                                    'publisher': image_info['publisher'],
                                    'offer': image_info['offer'],
                                    'sku': sku.name,
                                    'version': 'latest',
                                    'displayName': f"{image_info['display_prefix']} - {sku.name}"
                                })
                            except Exception as sku_e:
                                print(f"获取SKU {sku.name} 失败: {str(sku_e)}")
                                continue

                except Exception as offer_e:
                    print(f"获取 {image_info['offer']} 失败: {str(offer_e)}")
                    continue

            # 如果没有获取到任何镜像，返回一些默认选项
            if not images:
                images = [
                    {
                        'publisher': 'Canonical',
                        'offer': '0001-com-ubuntu-server-focal',
                        'sku': '20_04-lts-gen2',
                        'version': 'latest',
                        'displayName': 'Ubuntu 20.04 LTS - 20_04-lts-gen2'
                    },
                    {
                        'publisher': 'MicrosoftWindowsServer',
                        'offer': 'WindowsServer',
                        'sku': '2022-datacenter-g2',
                        'version': 'latest',
                        'displayName': 'Windows Server 2022 Datacenter'
                    }
                ]

            return images

        except Exception as e:
            print(f"获取虚拟机镜像失败: {str(e)}")
            # 返回默认镜像选项
            return [
                {
                    'publisher': 'Canonical',
                    'offer': '0001-com-ubuntu-server-focal',
                    'sku': '20_04-lts-gen2',
                    'version': 'latest',
                    'displayName': 'Ubuntu 20.04 LTS - 20_04-lts-gen2'
                },
                {
                    'publisher': 'MicrosoftWindowsServer',
                    'offer': 'WindowsServer',
                    'sku': '2022-datacenter-g2',
                    'version': 'latest',
                    'displayName': 'Windows Server 2022 Datacenter'
                }
            ]
            
    def get_resource_groups(self):
        """
        获取资源组列表
        
        Returns:
            list: 资源组列表
        """
        try:
            if not self.resource_client:
                return []
                
            resource_groups = []
            rg_list = self.resource_client.resource_groups.list()
            
            for rg in rg_list:
                resource_groups.append({
                    'name': rg.name,
                    'location': rg.location,
                    'id': rg.id,
                    'tags': rg.tags or {}
                })
                
            return sorted(resource_groups, key=lambda x: x['name'])
            
        except Exception as e:
            print(f"获取资源组列表失败: {str(e)}")
            return []

    def get_resource_group_details(self, resource_group_name):
        """
        获取资源组详细信息

        Args:
            resource_group_name (str): 资源组名称

        Returns:
            dict: 资源组详细信息
        """
        try:
            if not self.resource_client:
                return {}

            # 获取资源组基本信息
            rg = self.resource_client.resource_groups.get(resource_group_name)

            # 获取资源组中的资源列表
            resources = []
            try:
                resource_list = self.resource_client.resources.list_by_resource_group(resource_group_name)
                for resource in resource_list:
                    resources.append({
                        'name': resource.name,
                        'type': resource.type,
                        'location': resource.location,
                        'id': resource.id
                    })
            except Exception as res_e:
                print(f"获取资源列表失败: {str(res_e)}")

            # 获取虚拟机列表（包含IP信息）
            vms_with_ips = []
            try:
                if self.compute_client and self.network_client:
                    vm_list = self.compute_client.virtual_machines.list(resource_group_name)
                    for vm in vm_list:
                        vm_info = {
                            'name': vm.name,
                            'size': str(vm.hardware_profile.vm_size) if vm.hardware_profile.vm_size else 'Unknown',
                            'location': vm.location,
                            'public_ips': [],
                            'private_ips': []
                        }

                        # 获取网络接口信息
                        if vm.network_profile and vm.network_profile.network_interfaces:
                            for nic_ref in vm.network_profile.network_interfaces:
                                try:
                                    nic_id = nic_ref.id
                                    nic_name = nic_id.split('/')[-1]
                                    nic = self.network_client.network_interfaces.get(resource_group_name, nic_name)

                                    # 获取私有IP
                                    if nic.ip_configurations:
                                        for ip_config in nic.ip_configurations:
                                            if ip_config.private_ip_address:
                                                vm_info['private_ips'].append(ip_config.private_ip_address)

                                            # 获取公共IP
                                            if ip_config.public_ip_address:
                                                try:
                                                    public_ip_id = ip_config.public_ip_address.id
                                                    public_ip_name = public_ip_id.split('/')[-1]
                                                    public_ip = self.network_client.public_ip_addresses.get(resource_group_name, public_ip_name)
                                                    if public_ip.ip_address:
                                                        vm_info['public_ips'].append(public_ip.ip_address)
                                                except Exception as pip_e:
                                                    print(f"获取公共IP失败: {str(pip_e)}")

                                except Exception as nic_e:
                                    print(f"获取网络接口信息失败: {str(nic_e)}")

                        vms_with_ips.append(vm_info)

            except Exception as vm_e:
                print(f"获取虚拟机信息失败: {str(vm_e)}")

            return {
                'name': rg.name,
                'location': rg.location,
                'tags': rg.tags or {},
                'id': rg.id,
                'properties': rg.properties,
                'resources': resources,
                'virtual_machines': vms_with_ips
            }

        except Exception as e:
            print(f"获取资源组详情失败: {str(e)}")
            return {}
            
    def get_virtual_machines(self):
        """
        获取虚拟机列表
        
        Returns:
            list: 虚拟机列表
        """
        try:
            if not self.compute_client:
                return []
                
            vms = []
            vm_list = self.compute_client.virtual_machines.list_all()
            
            for vm in vm_list:
                try:
                    # 获取虚拟机状态
                    resource_group = vm.id.split('/')[4]  # 资源组名称
                    instance_view = self.compute_client.virtual_machines.instance_view(
                        resource_group,
                        vm.name
                    )

                    # 获取电源状态
                    power_state = "Unknown"
                    if instance_view.statuses:
                        for status in instance_view.statuses:
                            if status.code and status.code.startswith('PowerState/'):
                                # 处理不同类型的状态显示
                                if hasattr(status, 'display_status'):
                                    if hasattr(status.display_status, 'value'):
                                        power_state = status.display_status.value
                                    else:
                                        power_state = str(status.display_status)
                                elif hasattr(status, 'code'):
                                    power_state = status.code.replace('PowerState/', '')
                                break

                    # 处理虚拟机大小
                    vm_size = "Unknown"
                    if vm.hardware_profile and vm.hardware_profile.vm_size:
                        if hasattr(vm.hardware_profile.vm_size, 'value'):
                            vm_size = vm.hardware_profile.vm_size.value
                        else:
                            vm_size = str(vm.hardware_profile.vm_size)

                    # 处理操作系统类型
                    os_type = "Unknown"
                    if (vm.storage_profile and vm.storage_profile.os_disk and
                        vm.storage_profile.os_disk.os_type):
                        if hasattr(vm.storage_profile.os_disk.os_type, 'value'):
                            os_type = vm.storage_profile.os_disk.os_type.value
                        else:
                            os_type = str(vm.storage_profile.os_disk.os_type)

                    vms.append({
                        'name': vm.name,
                        'resourceGroup': resource_group,
                        'location': vm.location,
                        'vmSize': vm_size,
                        'powerState': power_state,
                        'id': vm.id,
                        'osType': os_type
                    })

                except Exception as vm_e:
                    print(f"获取虚拟机 {vm.name} 详细信息失败: {str(vm_e)}")
                    # 添加基本信息，即使获取详细信息失败
                    vms.append({
                        'name': vm.name,
                        'resourceGroup': vm.id.split('/')[4] if '/' in vm.id else 'Unknown',
                        'location': vm.location if hasattr(vm, 'location') else 'Unknown',
                        'vmSize': 'Unknown',
                        'powerState': 'Unknown',
                        'id': vm.id,
                        'osType': 'Unknown'
                    })
                
            return sorted(vms, key=lambda x: x['name'])
            
        except Exception as e:
            print(f"获取虚拟机列表失败: {str(e)}")
            return []

    def get_network_security_groups(self):
        """
        获取网络安全组列表

        Returns:
            list: 网络安全组列表
        """
        try:
            if not self.network_client:
                return []

            nsgs = []
            nsg_list = self.network_client.network_security_groups.list_all()

            for nsg in nsg_list:
                nsgs.append({
                    'name': nsg.name,
                    'resourceGroup': nsg.id.split('/')[4],  # 从资源ID中提取资源组名称
                    'location': nsg.location,
                    'id': nsg.id,
                    'tags': nsg.tags or {}
                })

            return sorted(nsgs, key=lambda x: x['name'])

        except Exception as e:
            print(f"获取网络安全组列表失败: {str(e)}")
            return []

    def get_network_security_group_rules(self, resource_group_name, nsg_name):
        """
        获取网络安全组规则

        Args:
            resource_group_name (str): 资源组名称
            nsg_name (str): 网络安全组名称

        Returns:
            list: 安全规则列表
        """
        try:
            if not self.network_client:
                return []

            nsg = self.network_client.network_security_groups.get(
                resource_group_name, nsg_name
            )

            rules = []

            # 获取入站规则
            if nsg.security_rules:
                for rule in nsg.security_rules:
                    rules.append({
                        'name': rule.name,
                        'direction': rule.direction.value if hasattr(rule.direction, 'value') else str(rule.direction),
                        'priority': rule.priority,
                        'protocol': rule.protocol.value if hasattr(rule.protocol, 'value') else str(rule.protocol),
                        'sourcePortRange': rule.source_port_range or '*',
                        'destinationPortRange': rule.destination_port_range or '*',
                        'sourceAddressPrefix': rule.source_address_prefix or '*',
                        'destinationAddressPrefix': rule.destination_address_prefix or '*',
                        'access': rule.access.value if hasattr(rule.access, 'value') else str(rule.access)
                    })

            # 获取默认规则
            if nsg.default_security_rules:
                for rule in nsg.default_security_rules:
                    rules.append({
                        'name': f"{rule.name} (默认)",
                        'direction': rule.direction.value if hasattr(rule.direction, 'value') else str(rule.direction),
                        'priority': rule.priority,
                        'protocol': rule.protocol.value if hasattr(rule.protocol, 'value') else str(rule.protocol),
                        'sourcePortRange': rule.source_port_range or '*',
                        'destinationPortRange': rule.destination_port_range or '*',
                        'sourceAddressPrefix': rule.source_address_prefix or '*',
                        'destinationAddressPrefix': rule.destination_address_prefix or '*',
                        'access': rule.access.value if hasattr(rule.access, 'value') else str(rule.access)
                    })

            return sorted(rules, key=lambda x: x['priority'])

        except Exception as e:
            print(f"获取网络安全组规则失败: {str(e)}")
            return []

    def create_security_rule(self, resource_group_name, nsg_name, rule_config):
        """
        创建网络安全组规则

        Args:
            resource_group_name (str): 资源组名称
            nsg_name (str): 网络安全组名称
            rule_config (dict): 规则配置

        Returns:
            dict: 创建结果
        """
        try:
            if not self.network_client:
                return {'success': False, 'error': '网络客户端未初始化'}

            from azure.mgmt.network.models import (
                SecurityRule, SecurityRuleAccess, SecurityRuleDirection, SecurityRuleProtocol
            )

            # 解析访问权限
            access = SecurityRuleAccess.allow if rule_config['access'].lower() == 'allow' else SecurityRuleAccess.deny

            # 解析方向
            direction = SecurityRuleDirection.inbound if rule_config['direction'].lower() == 'inbound' else SecurityRuleDirection.outbound

            # 解析协议
            protocol_map = {
                'tcp': SecurityRuleProtocol.tcp,
                'udp': SecurityRuleProtocol.udp,
                'icmp': SecurityRuleProtocol.icmp,
                '*': SecurityRuleProtocol.asterisk
            }
            protocol = protocol_map.get(rule_config['protocol'].lower(), SecurityRuleProtocol.tcp)

            # 创建安全规则对象
            security_rule = SecurityRule(
                name=rule_config['name'],
                protocol=protocol,
                source_port_range=rule_config.get('source_port_range', '*'),
                destination_port_range=rule_config.get('destination_port_range', '*'),
                source_address_prefix=rule_config.get('source_address_prefix', '*'),
                destination_address_prefix=rule_config.get('destination_address_prefix', '*'),
                access=access,
                direction=direction,
                priority=int(rule_config['priority'])
            )

            # 创建规则
            operation = self.network_client.security_rules.begin_create_or_update(
                resource_group_name=resource_group_name,
                network_security_group_name=nsg_name,
                security_rule_name=rule_config['name'],
                security_rule_parameters=security_rule
            )

            # 等待操作完成
            result = operation.result()

            return {
                'success': True,
                'rule_id': result.id,
                'rule_name': result.name
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"创建安全规则失败: {str(e)}"
            }

    def delete_security_rule(self, resource_group_name, nsg_name, rule_name):
        """
        删除网络安全组规则

        Args:
            resource_group_name (str): 资源组名称
            nsg_name (str): 网络安全组名称
            rule_name (str): 规则名称

        Returns:
            dict: 删除结果
        """
        try:
            if not self.network_client:
                return {'success': False, 'error': '网络客户端未初始化'}

            # 删除规则
            operation = self.network_client.security_rules.begin_delete(
                resource_group_name=resource_group_name,
                network_security_group_name=nsg_name,
                security_rule_name=rule_name
            )

            # 等待操作完成
            operation.result()

            return {
                'success': True,
                'message': f"安全规则 {rule_name} 删除成功"
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"删除安全规则失败: {str(e)}"
            }

    def delete_resource_group(self, resource_group_name, progress_callback=None):
        """
        删除资源组

        Args:
            resource_group_name (str): 资源组名称
            progress_callback (function): 进度回调函数

        Returns:
            dict: 删除结果
        """
        def update_progress(value, message):
            if progress_callback:
                progress_callback(value, message)

        try:
            if not self.resource_client:
                return {'success': False, 'error': '资源客户端未初始化'}

            update_progress(10, f"正在验证资源组 {resource_group_name}...")

            # 检查资源组是否存在
            try:
                self.resource_client.resource_groups.get(resource_group_name)
            except Exception:
                return {'success': False, 'error': f'资源组 {resource_group_name} 不存在'}

            update_progress(20, "正在检查资源组中的资源...")

            # 获取资源组中的资源列表
            try:
                resources = list(self.resource_client.resources.list_by_resource_group(resource_group_name))
                resource_count = len(resources)
                update_progress(30, f"发现 {resource_count} 个资源需要删除")
            except Exception as e:
                print(f"获取资源列表失败: {str(e)}")
                resource_count = 0

            update_progress(40, "开始删除资源组...")

            # 开始删除资源组（这是一个长时间运行的操作）
            delete_operation = self.resource_client.resource_groups.begin_delete(resource_group_name)

            update_progress(50, "资源组删除操作已启动，正在等待完成...")

            # 等待删除完成，定期更新进度
            import time
            progress = 50
            while not delete_operation.done():
                time.sleep(5)  # 每5秒检查一次
                progress = min(progress + 5, 90)  # 逐渐增加进度，但不超过90%

                if resource_count > 0:
                    update_progress(progress, f"正在删除资源组中的 {resource_count} 个资源...")
                else:
                    update_progress(progress, "正在删除资源组...")

            # 等待操作完全完成
            result = delete_operation.result()

            update_progress(100, f"资源组 {resource_group_name} 删除完成")

            return {
                'success': True,
                'message': f"资源组 {resource_group_name} 及其所有资源已成功删除"
            }

        except Exception as e:
            error_msg = f"删除资源组失败: {str(e)}"
            update_progress(0, error_msg)
            return {
                'success': False,
                'error': error_msg
            }

    def get_all_publishers(self, location):
        """
        获取所有虚拟机镜像发布者

        Args:
            location (str): 位置名称

        Returns:
            list: 发布者列表
        """
        try:
            if not self.compute_client:
                return []

            publishers = []
            publisher_list = self.compute_client.virtual_machine_images.list_publishers(location=location)

            # 定义常用的发布者，优先处理这些
            priority_publishers = [
                'Canonical', 'MicrosoftWindowsServer', 'RedHat', 'OpenLogic',
                'SUSE', 'Oracle', 'Debian', 'MicrosoftSQLServer', 'microsoft-ads'
            ]

            # 先添加优先发布者
            for pub in publisher_list:
                if pub.name in priority_publishers:
                    publishers.append({'name': pub.name, 'priority': True})

            # 再添加其他发布者（限制数量）
            other_count = 0
            for pub in publisher_list:
                if pub.name not in priority_publishers and other_count < 20:
                    publishers.append({'name': pub.name, 'priority': False})
                    other_count += 1

            return publishers

        except Exception as e:
            print(f"获取发布者列表失败: {str(e)}")
            return []

    def create_virtual_machine(self, vm_config, progress_callback=None):
        """
        创建虚拟机

        Args:
            vm_config (dict): 虚拟机配置信息
            progress_callback (function): 进度回调函数

        Returns:
            dict: 创建结果
        """
        def update_progress(value, message):
            if progress_callback:
                progress_callback(value, message)
        try:
            if not self.resource_client or not self.compute_client or not self.network_client:
                return {'success': False, 'error': '客户端未初始化'}

            rg_name = vm_config['rg_name']
            vm_name = vm_config['vm_name']
            location = vm_config['location']
            vm_size = vm_config['vm_size']
            vm_image = vm_config['vm_image']
            username = vm_config['username']
            password = vm_config['password']

            # 1. 创建资源组（如果不存在）
            update_progress(15, "正在创建资源组...")
            try:
                self.resource_client.resource_groups.get(rg_name)
                update_progress(20, "资源组已存在")
            except:
                # 资源组不存在，创建新的
                from azure.mgmt.resource.resources.models import ResourceGroup
                rg_params = ResourceGroup(location=location)
                self.resource_client.resource_groups.create_or_update(rg_name, rg_params)
                update_progress(20, "资源组创建完成")

            # 2. 创建虚拟网络
            update_progress(25, "正在创建网络安全组...")
            vnet_name = f"{vm_name}-vnet"
            subnet_name = f"{vm_name}-subnet"

            from azure.mgmt.network.models import (
                VirtualNetwork, AddressSpace, Subnet, NetworkSecurityGroup,
                SecurityRule, SecurityRuleAccess, SecurityRuleDirection,
                SecurityRuleProtocol, PublicIPAddress, PublicIPAddressSku,
                PublicIPAddressSkuName, IPAllocationMethod, NetworkInterface,
                NetworkInterfaceIPConfiguration
            )

            # 创建网络安全组
            nsg_name = f"{vm_name}-nsg"
            nsg_rules = []

            # 根据配置添加安全规则
            priority = 1000
            if vm_config.get('open_ssh', False):
                ssh_rule = SecurityRule(
                    name='AllowSSH',
                    protocol=SecurityRuleProtocol.tcp,
                    source_port_range='*',
                    destination_port_range='22',
                    source_address_prefix='*',
                    destination_address_prefix='*',
                    access=SecurityRuleAccess.allow,
                    direction=SecurityRuleDirection.inbound,
                    priority=priority
                )
                nsg_rules.append(ssh_rule)
                priority += 10

            if vm_config.get('open_http', False):
                http_rule = SecurityRule(
                    name='AllowHTTP',
                    protocol=SecurityRuleProtocol.tcp,
                    source_port_range='*',
                    destination_port_range='80',
                    source_address_prefix='*',
                    destination_address_prefix='*',
                    access=SecurityRuleAccess.allow,
                    direction=SecurityRuleDirection.inbound,
                    priority=priority
                )
                nsg_rules.append(http_rule)
                priority += 10

            if vm_config.get('open_https', False):
                https_rule = SecurityRule(
                    name='AllowHTTPS',
                    protocol=SecurityRuleProtocol.tcp,
                    source_port_range='*',
                    destination_port_range='443',
                    source_address_prefix='*',
                    destination_address_prefix='*',
                    access=SecurityRuleAccess.allow,
                    direction=SecurityRuleDirection.inbound,
                    priority=priority
                )
                nsg_rules.append(https_rule)

            nsg_params = NetworkSecurityGroup(
                location=location,
                security_rules=nsg_rules
            )

            nsg_result = self.network_client.network_security_groups.begin_create_or_update(
                rg_name, nsg_name, nsg_params
            ).result()

            update_progress(40, "正在创建虚拟网络...")
            # 创建虚拟网络和子网
            vnet_params = VirtualNetwork(
                location=location,
                address_space=AddressSpace(address_prefixes=['10.0.0.0/16']),
                subnets=[
                    Subnet(
                        name=subnet_name,
                        address_prefix='10.0.0.0/24',
                        network_security_group=nsg_result
                    )
                ]
            )

            vnet_result = self.network_client.virtual_networks.begin_create_or_update(
                rg_name, vnet_name, vnet_params
            ).result()

            # 3. 创建公共IP（如果需要）
            update_progress(55, "正在创建公共IP...")
            public_ip_result = None
            if vm_config.get('create_public_ip', False):
                public_ip_name = vm_config.get('public_ip_name', f"{vm_name}-ip")
                public_ip_params = PublicIPAddress(
                    location=location,
                    sku=PublicIPAddressSku(name=PublicIPAddressSkuName.standard),
                    public_ip_allocation_method=IPAllocationMethod.static,
                    dns_settings={'domain_name_label': vm_name.lower()}
                )

                public_ip_result = self.network_client.public_ip_addresses.begin_create_or_update(
                    rg_name, public_ip_name, public_ip_params
                ).result()

            # 4. 创建网络接口
            update_progress(70, "正在创建网络接口...")
            nic_name = f"{vm_name}-nic"
            subnet = vnet_result.subnets[0]

            ip_config = NetworkInterfaceIPConfiguration(
                name=f"{vm_name}-ip-config",
                subnet=subnet,
                private_ip_allocation_method=IPAllocationMethod.dynamic
            )

            if public_ip_result:
                ip_config.public_ip_address = public_ip_result

            nic_params = NetworkInterface(
                location=location,
                ip_configurations=[ip_config]
            )

            nic_result = self.network_client.network_interfaces.begin_create_or_update(
                rg_name, nic_name, nic_params
            ).result()

            # 5. 创建虚拟机
            update_progress(85, "正在创建虚拟机...")
            from azure.mgmt.compute.models import (
                VirtualMachine, HardwareProfile, StorageProfile, OSProfile,
                NetworkProfile, NetworkInterfaceReference, OSDisk,
                DiskCreateOptionTypes, ImageReference, LinuxConfiguration,
                SshConfiguration, SshPublicKey, WindowsConfiguration
            )

            # 构建镜像引用
            if isinstance(vm_image, dict):
                image_ref = ImageReference(
                    publisher=vm_image['publisher'],
                    offer=vm_image['offer'],
                    sku=vm_image['sku'],
                    version=vm_image['version']
                )
            else:
                # 默认使用Ubuntu镜像
                image_ref = ImageReference(
                    publisher='Canonical',
                    offer='0001-com-ubuntu-server-focal',
                    sku='20_04-lts-gen2',
                    version='latest'
                )

            # 操作系统配置
            os_profile = OSProfile(
                computer_name=vm_name,
                admin_username=username,
                admin_password=password
            )

            # 如果是Linux系统，禁用密码认证并启用SSH
            if 'ubuntu' in str(vm_image).lower() or 'linux' in str(vm_image).lower():
                os_profile.linux_configuration = LinuxConfiguration(
                    disable_password_authentication=False
                )
            else:
                # Windows配置
                os_profile.windows_configuration = WindowsConfiguration(
                    enable_automatic_updates=True
                )

            vm_params = VirtualMachine(
                location=location,
                hardware_profile=HardwareProfile(vm_size=vm_size),
                storage_profile=StorageProfile(
                    image_reference=image_ref,
                    os_disk=OSDisk(
                        name=f"{vm_name}-os-disk",
                        create_option=DiskCreateOptionTypes.from_image,
                        disk_size_gb=vm_config.get('disk_size', 64)
                    )
                ),
                os_profile=os_profile,
                network_profile=NetworkProfile(
                    network_interfaces=[
                        NetworkInterfaceReference(id=nic_result.id)
                    ]
                )
            )

            # 开始创建虚拟机（异步操作）
            update_progress(85, f"正在创建虚拟机 {vm_name}...")
            vm_operation = self.compute_client.virtual_machines.begin_create_or_update(
                rg_name, vm_name, vm_params
            )

            # 等待虚拟机创建完成
            update_progress(90, f"等待虚拟机 {vm_name} 创建完成...")
            vm_result = vm_operation.result()

            update_progress(95, f"虚拟机 {vm_name} 创建完成，正在获取最终状态...")

            # 获取公共IP地址（如果有）
            final_public_ip = None
            if public_ip_result:
                try:
                    # 刷新公共IP信息以获取分配的地址
                    refreshed_ip = self.network_client.public_ip_addresses.get(rg_name, public_ip_result.name)
                    final_public_ip = refreshed_ip.ip_address
                except:
                    final_public_ip = "正在分配中"

            return {
                'success': True,
                'vm_id': vm_result.id,
                'vm_name': vm_name,
                'resource_group': rg_name,
                'location': location,
                'public_ip': final_public_ip,
                'vm_size': vm_size,
                'creation_time': vm_result.time_created.isoformat() if hasattr(vm_result, 'time_created') and vm_result.time_created else None
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"创建虚拟机失败: {str(e)}"
            }
