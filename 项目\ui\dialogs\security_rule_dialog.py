#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全规则添加/编辑对话框
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QComboBox, QSpinBox, QPushButton,
                             QLabel, QMessageBox, QGroupBox)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont


class SecurityRuleDialog(QDialog):
    """安全规则添加/编辑对话框"""
    
    def __init__(self, parent=None, rule_data=None):
        super().__init__(parent)
        self.rule_data = rule_data  # 如果是编辑模式，传入现有规则数据
        self.init_ui()
        
        if rule_data:
            self.load_rule_data(rule_data)
        
    def init_ui(self):
        """初始化UI"""
        title = "编辑安全规则" if self.rule_data else "添加安全规则"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)
        
        # 规则名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入规则名称")
        basic_layout.addRow("规则名称:", self.name_edit)
        
        # 优先级
        self.priority_spin = QSpinBox()
        self.priority_spin.setRange(100, 4096)
        self.priority_spin.setValue(1000)
        basic_layout.addRow("优先级:", self.priority_spin)
        
        # 方向
        self.direction_combo = QComboBox()
        self.direction_combo.addItems(["Inbound", "Outbound"])
        basic_layout.addRow("方向:", self.direction_combo)
        
        # 访问权限
        self.access_combo = QComboBox()
        self.access_combo.addItems(["Allow", "Deny"])
        basic_layout.addRow("访问权限:", self.access_combo)
        
        layout.addWidget(basic_group)
        
        # 协议和端口组
        protocol_group = QGroupBox("协议和端口")
        protocol_layout = QFormLayout(protocol_group)
        
        # 协议
        self.protocol_combo = QComboBox()
        self.protocol_combo.addItems(["TCP", "UDP", "ICMP", "*"])
        protocol_layout.addRow("协议:", self.protocol_combo)
        
        # 源端口范围
        self.source_port_edit = QLineEdit()
        self.source_port_edit.setPlaceholderText("例如: 80, 80-90, * (默认为 *)")
        self.source_port_edit.setText("*")
        protocol_layout.addRow("源端口范围:", self.source_port_edit)
        
        # 目标端口范围
        self.dest_port_edit = QLineEdit()
        self.dest_port_edit.setPlaceholderText("例如: 80, 80-90, *")
        protocol_layout.addRow("目标端口范围:", self.dest_port_edit)
        
        layout.addWidget(protocol_group)
        
        # 地址组
        address_group = QGroupBox("源和目标地址")
        address_layout = QFormLayout(address_group)
        
        # 源地址前缀
        self.source_address_edit = QLineEdit()
        self.source_address_edit.setPlaceholderText("例如: *, ***********/24, Internet")
        self.source_address_edit.setText("*")
        address_layout.addRow("源地址前缀:", self.source_address_edit)
        
        # 目标地址前缀
        self.dest_address_edit = QLineEdit()
        self.dest_address_edit.setPlaceholderText("例如: *, ***********/24, VirtualNetwork")
        self.dest_address_edit.setText("*")
        address_layout.addRow("目标地址前缀:", self.dest_address_edit)
        
        layout.addWidget(address_group)
        
        # 常用规则快速设置
        quick_group = QGroupBox("常用规则快速设置")
        quick_layout = QHBoxLayout(quick_group)
        
        ssh_btn = QPushButton("SSH (22)")
        ssh_btn.clicked.connect(self.set_ssh_rule)
        quick_layout.addWidget(ssh_btn)
        
        http_btn = QPushButton("HTTP (80)")
        http_btn.clicked.connect(self.set_http_rule)
        quick_layout.addWidget(http_btn)
        
        https_btn = QPushButton("HTTPS (443)")
        https_btn.clicked.connect(self.set_https_rule)
        quick_layout.addWidget(https_btn)
        
        rdp_btn = QPushButton("RDP (3389)")
        rdp_btn.clicked.connect(self.set_rdp_rule)
        quick_layout.addWidget(rdp_btn)
        
        quick_layout.addStretch()
        layout.addWidget(quick_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept_rule)
        button_layout.addWidget(self.ok_btn)
        
        layout.addLayout(button_layout)
        
    def set_ssh_rule(self):
        """设置SSH规则"""
        self.name_edit.setText("AllowSSH")
        self.protocol_combo.setCurrentText("TCP")
        self.dest_port_edit.setText("22")
        self.direction_combo.setCurrentText("Inbound")
        self.access_combo.setCurrentText("Allow")
        
    def set_http_rule(self):
        """设置HTTP规则"""
        self.name_edit.setText("AllowHTTP")
        self.protocol_combo.setCurrentText("TCP")
        self.dest_port_edit.setText("80")
        self.direction_combo.setCurrentText("Inbound")
        self.access_combo.setCurrentText("Allow")
        
    def set_https_rule(self):
        """设置HTTPS规则"""
        self.name_edit.setText("AllowHTTPS")
        self.protocol_combo.setCurrentText("TCP")
        self.dest_port_edit.setText("443")
        self.direction_combo.setCurrentText("Inbound")
        self.access_combo.setCurrentText("Allow")
        
    def set_rdp_rule(self):
        """设置RDP规则"""
        self.name_edit.setText("AllowRDP")
        self.protocol_combo.setCurrentText("TCP")
        self.dest_port_edit.setText("3389")
        self.direction_combo.setCurrentText("Inbound")
        self.access_combo.setCurrentText("Allow")
        
    def load_rule_data(self, rule_data):
        """加载现有规则数据（编辑模式）"""
        self.name_edit.setText(rule_data.get('name', ''))
        self.priority_spin.setValue(int(rule_data.get('priority', 1000)))
        self.direction_combo.setCurrentText(rule_data.get('direction', 'Inbound'))
        self.access_combo.setCurrentText(rule_data.get('access', 'Allow'))
        self.protocol_combo.setCurrentText(rule_data.get('protocol', 'TCP'))
        self.source_port_edit.setText(rule_data.get('sourcePortRange', '*'))
        self.dest_port_edit.setText(rule_data.get('destinationPortRange', '*'))
        self.source_address_edit.setText(rule_data.get('sourceAddressPrefix', '*'))
        self.dest_address_edit.setText(rule_data.get('destinationAddressPrefix', '*'))
        
    def accept_rule(self):
        """确认规则"""
        # 验证输入
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "警告", "请输入规则名称")
            return
            
        if not self.dest_port_edit.text().strip():
            QMessageBox.warning(self, "警告", "请输入目标端口范围")
            return
            
        self.accept()
        
    def get_rule_config(self):
        """获取规则配置"""
        return {
            'name': self.name_edit.text().strip(),
            'priority': self.priority_spin.value(),
            'direction': self.direction_combo.currentText(),
            'access': self.access_combo.currentText(),
            'protocol': self.protocol_combo.currentText(),
            'source_port_range': self.source_port_edit.text().strip() or '*',
            'destination_port_range': self.dest_port_edit.text().strip() or '*',
            'source_address_prefix': self.source_address_edit.text().strip() or '*',
            'destination_address_prefix': self.dest_address_edit.text().strip() or '*'
        }
