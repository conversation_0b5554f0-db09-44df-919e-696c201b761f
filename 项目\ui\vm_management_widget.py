#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟机管理组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QProgressBar, QLabel, QMessageBox, QComboBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont


class VMManagementWorker(QThread):
    """虚拟机管理工作线程"""
    progress_update = pyqtSignal(int, str)
    data_loaded = pyqtSignal(list)
    operation_complete = pyqtSignal(str)
    operation_failed = pyqtSignal(str)
    
    def __init__(self, auth_client, operation, **kwargs):
        super().__init__()
        self.auth_client = auth_client
        self.operation = operation
        self.kwargs = kwargs
        
    def run(self):
        """执行操作"""
        try:
            if self.operation == 'load_vms':
                self.load_virtual_machines()
            elif self.operation == 'load_resource_groups':
                self.load_resource_groups()
            elif self.operation == 'delete_resource_group':
                self.delete_resource_group()
                
        except Exception as e:
            self.operation_failed.emit(f"操作失败: {str(e)}")
            
    def load_virtual_machines(self):
        """加载虚拟机列表"""
        self.progress_update.emit(20, "正在获取虚拟机列表...")
        vms = self.auth_client.get_virtual_machines()

        self.progress_update.emit(40, "正在获取资源组信息...")
        resource_groups = self.auth_client.get_resource_groups()

        # 为每个虚拟机获取详细的IP信息
        self.progress_update.emit(60, "正在获取虚拟机IP信息...")
        enhanced_vms = []
        for i, vm in enumerate(vms):
            try:
                # 获取该虚拟机所在资源组的详细信息
                rg_details = self.auth_client.get_resource_group_details(vm['resourceGroup'])

                # 查找对应的虚拟机信息
                vm_with_ip = vm.copy()
                for detailed_vm in rg_details.get('virtual_machines', []):
                    if detailed_vm['name'] == vm['name']:
                        vm_with_ip['public_ip'] = ', '.join(detailed_vm['public_ips']) if detailed_vm['public_ips'] else '无'
                        vm_with_ip['private_ip'] = ', '.join(detailed_vm['private_ips']) if detailed_vm['private_ips'] else '无'
                        break
                else:
                    vm_with_ip['public_ip'] = '无'
                    vm_with_ip['private_ip'] = '无'

                enhanced_vms.append(vm_with_ip)

                # 更新进度
                progress = 60 + (i + 1) * 30 // len(vms)
                self.progress_update.emit(progress, f"正在获取虚拟机IP信息... ({i+1}/{len(vms)})")

            except Exception as e:
                print(f"获取虚拟机 {vm['name']} IP信息失败: {str(e)}")
                vm_with_ip = vm.copy()
                vm_with_ip['public_ip'] = '获取失败'
                vm_with_ip['private_ip'] = '获取失败'
                enhanced_vms.append(vm_with_ip)

        self.progress_update.emit(100, "加载完成")

        result = {
            'vms': enhanced_vms,
            'resource_groups': resource_groups
        }

        self.data_loaded.emit([result])
        
    def load_resource_groups(self):
        """加载资源组列表"""
        self.progress_update.emit(50, "正在获取资源组列表...")
        resource_groups = self.auth_client.get_resource_groups()
        
        self.progress_update.emit(100, "加载完成")
        self.data_loaded.emit(resource_groups)
        
    def delete_resource_group(self):
        """删除资源组"""
        rg_name = self.kwargs.get('rg_name')
        
        self.progress_update.emit(20, f"正在删除资源组: {rg_name}")
        
        # 这里应该调用Azure API删除资源组
        # 由于删除操作比较危险，这里只是模拟
        import time
        time.sleep(2)  # 模拟删除过程
        
        self.progress_update.emit(100, "删除完成")
        self.operation_complete.emit(f"资源组 {rg_name} 已删除")


class VMManagementWidget(QWidget):
    """虚拟机管理组件"""
    
    def __init__(self):
        super().__init__()
        self.auth_info = None
        self.vms_data = []
        self.resource_groups_data = []
        self.init_ui()
        self.setEnabled(False)  # 初始状态禁用
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 操作按钮区域
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新列表")
        self.refresh_btn.clicked.connect(self.refresh_data)
        button_layout.addWidget(self.refresh_btn)
        
        self.delete_rg_btn = QPushButton("删除资源组")
        self.delete_rg_btn.clicked.connect(self.delete_selected_resource_group)
        self.delete_rg_btn.setEnabled(False)
        button_layout.addWidget(self.delete_rg_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("未加载数据")
        layout.addWidget(self.status_label)
        
        # 虚拟机表格
        vm_label = QLabel("虚拟机列表:")
        vm_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(vm_label)
        
        self.vm_table = QTableWidget()
        self.vm_table.setColumnCount(6)
        self.vm_table.setHorizontalHeaderLabels([
            "名称", "资源组", "位置", "规格", "状态", "操作系统"
        ])
        
        # 设置表格属性
        header = self.vm_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.vm_table.setAlternatingRowColors(True)
        self.vm_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        layout.addWidget(self.vm_table)
        
        # 资源组表格
        rg_label = QLabel("资源组列表:")
        rg_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(rg_label)
        
        self.rg_table = QTableWidget()
        self.rg_table.setColumnCount(3)
        self.rg_table.setHorizontalHeaderLabels([
            "名称", "位置", "标签"
        ])
        
        # 设置表格属性
        rg_header = self.rg_table.horizontalHeader()
        rg_header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.rg_table.setAlternatingRowColors(True)
        self.rg_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.rg_table.itemSelectionChanged.connect(self.on_rg_selection_changed)
        
        layout.addWidget(self.rg_table)
        
    def set_auth_info(self, auth_info):
        """设置认证信息"""
        self.auth_info = auth_info
        self.refresh_data()
        
    def refresh_data(self):
        """刷新数据"""
        if not self.auth_info or not self.auth_info.get('auth_client'):
            return
            
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.refresh_btn.setEnabled(False)
        self.status_label.setText("正在加载数据...")
        
        # 启动工作线程
        self.worker = VMManagementWorker(
            self.auth_info['auth_client'], 
            'load_vms'
        )
        self.worker.progress_update.connect(self.on_progress_update)
        self.worker.data_loaded.connect(self.on_data_loaded)
        self.worker.operation_failed.connect(self.on_operation_failed)
        self.worker.start()
        
    def on_progress_update(self, value, message):
        """进度更新"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        
    def on_data_loaded(self, data):
        """数据加载完成"""
        self.progress_bar.setVisible(False)
        self.refresh_btn.setEnabled(True)
        
        if data and len(data) > 0:
            result = data[0]
            self.vms_data = result.get('vms', [])
            self.resource_groups_data = result.get('resource_groups', [])
            
            self.update_vm_table()
            self.update_rg_table()
            
            self.status_label.setText(f"已加载 {len(self.vms_data)} 个虚拟机，{len(self.resource_groups_data)} 个资源组")
        else:
            self.status_label.setText("没有找到数据")
            
    def on_operation_failed(self, error_msg):
        """操作失败"""
        self.progress_bar.setVisible(False)
        self.refresh_btn.setEnabled(True)
        self.status_label.setText("操作失败")
        
        QMessageBox.critical(self, "错误", error_msg)
        
    def update_vm_table(self):
        """更新虚拟机表格"""
        self.vm_table.setRowCount(len(self.vms_data))
        
        for row, vm in enumerate(self.vms_data):
            self.vm_table.setItem(row, 0, QTableWidgetItem(vm.get('name', '')))
            self.vm_table.setItem(row, 1, QTableWidgetItem(vm.get('resourceGroup', '')))
            self.vm_table.setItem(row, 2, QTableWidgetItem(vm.get('location', '')))
            self.vm_table.setItem(row, 3, QTableWidgetItem(vm.get('vmSize', '')))
            self.vm_table.setItem(row, 4, QTableWidgetItem(vm.get('powerState', '')))
            self.vm_table.setItem(row, 5, QTableWidgetItem(vm.get('osType', '')))
            
    def update_rg_table(self):
        """更新资源组表格"""
        self.rg_table.setRowCount(len(self.resource_groups_data))
        
        for row, rg in enumerate(self.resource_groups_data):
            self.rg_table.setItem(row, 0, QTableWidgetItem(rg.get('name', '')))
            self.rg_table.setItem(row, 1, QTableWidgetItem(rg.get('location', '')))
            
            # 处理标签
            tags = rg.get('tags', {})
            tag_str = ', '.join([f"{k}:{v}" for k, v in tags.items()]) if tags else "无"
            self.rg_table.setItem(row, 2, QTableWidgetItem(tag_str))
            
    def on_rg_selection_changed(self):
        """资源组选择变化"""
        selected_rows = self.rg_table.selectionModel().selectedRows()
        self.delete_rg_btn.setEnabled(len(selected_rows) > 0)
        
    def delete_selected_resource_group(self):
        """删除选中的资源组"""
        selected_rows = self.rg_table.selectionModel().selectedRows()
        
        if not selected_rows:
            return
            
        row = selected_rows[0].row()
        rg_name = self.rg_table.item(row, 0).text()
        
        # 确认删除
        reply = QMessageBox.question(
            self, '确认删除', 
            f'确定要删除资源组 "{rg_name}" 吗？\n\n警告：这将删除资源组中的所有资源！',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.delete_rg_btn.setEnabled(False)
            
            # 启动删除线程
            self.delete_worker = VMManagementWorker(
                self.auth_info['auth_client'],
                'delete_resource_group',
                rg_name=rg_name
            )
            self.delete_worker.progress_update.connect(self.on_progress_update)
            self.delete_worker.operation_complete.connect(self.on_delete_complete)
            self.delete_worker.operation_failed.connect(self.on_operation_failed)
            self.delete_worker.start()
            
    def on_delete_complete(self, message):
        """删除完成"""
        self.progress_bar.setVisible(False)
        self.delete_rg_btn.setEnabled(True)
        
        QMessageBox.information(self, "删除成功", message)
        
        # 刷新数据
        self.refresh_data()
