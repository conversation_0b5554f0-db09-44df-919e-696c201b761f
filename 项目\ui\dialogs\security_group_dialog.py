#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全组管理对话框
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QLabel, QMessageBox, QComboBox, QProgressBar)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont


class SecurityGroupDialog(QDialog):
    """安全组管理对话框"""
    
    def __init__(self, security_widget, parent=None):
        super().__init__(parent)
        self.security_widget = security_widget
        self.init_ui()
        # 延迟加载数据，避免阻塞UI
        from PyQt6.QtCore import QTimer
        QTimer.singleShot(100, self.load_data)
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("网络安全组管理")
        self.setModal(True)
        self.resize(1200, 700)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("网络安全组管理")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 安全组选择区域
        nsg_layout = QHBoxLayout()

        nsg_label = QLabel("选择安全组:")
        nsg_layout.addWidget(nsg_label)

        self.nsg_combo = QComboBox()
        self.nsg_combo.currentTextChanged.connect(self.on_nsg_changed)
        nsg_layout.addWidget(self.nsg_combo)

        self.refresh_nsg_btn = QPushButton("刷新")
        self.refresh_nsg_btn.clicked.connect(self.refresh_security_groups)
        nsg_layout.addWidget(self.refresh_nsg_btn)

        nsg_layout.addStretch()
        layout.addLayout(nsg_layout)

        # 操作按钮区域
        button_layout = QHBoxLayout()

        self.add_rule_btn = QPushButton("添加规则")
        self.add_rule_btn.clicked.connect(self.add_security_rule)
        self.add_rule_btn.setEnabled(False)
        button_layout.addWidget(self.add_rule_btn)

        self.edit_rule_btn = QPushButton("编辑规则")
        self.edit_rule_btn.clicked.connect(self.edit_security_rule)
        self.edit_rule_btn.setEnabled(False)
        button_layout.addWidget(self.edit_rule_btn)

        self.delete_rule_btn = QPushButton("删除规则")
        self.delete_rule_btn.clicked.connect(self.delete_security_rule)
        self.delete_rule_btn.setEnabled(False)
        button_layout.addWidget(self.delete_rule_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("请选择安全组")
        layout.addWidget(self.status_label)

        # 规则表格
        rules_label = QLabel("安全规则:")
        rules_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(rules_label)

        self.rules_table = QTableWidget()
        self.rules_table.setColumnCount(9)
        self.rules_table.setHorizontalHeaderLabels([
            "名称", "方向", "优先级", "协议", "源端口", "目标端口", "源地址", "目标地址", "访问权限"
        ])

        # 设置表格属性
        header = self.rules_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.rules_table.setAlternatingRowColors(True)
        self.rules_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.rules_table.itemSelectionChanged.connect(self.on_rule_selection_changed)

        layout.addWidget(self.rules_table)
        
        # 规则说明
        info_label = QLabel("说明: 绿色表示允许规则，红色表示拒绝规则")
        info_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        layout.addWidget(info_label)
        
        # 关闭按钮
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        close_layout.addWidget(close_btn)
        
        layout.addLayout(close_layout)
        
    def load_data(self):
        """加载数据"""
        # 触发安全组数据加载
        self.refresh_security_groups()

    def refresh_security_groups(self):
        """刷新安全组列表"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(50)
        self.status_label.setText("正在加载安全组...")

        try:
            # 尝试从原始组件获取真实数据
            if hasattr(self.security_widget, 'auth_info') and self.security_widget.auth_info:
                auth_client = self.security_widget.auth_info.get('auth_client')
                if auth_client:
                    # 调用Azure API获取真实的网络安全组
                    security_groups = auth_client.get_network_security_groups()
                else:
                    security_groups = []
            else:
                security_groups = []

        except Exception as e:
            print(f"获取安全组失败: {str(e)}")
            security_groups = []

        self.nsg_combo.clear()
        if security_groups:
            for nsg in security_groups:
                display_text = f"{nsg['name']} ({nsg['resourceGroup']})"
                self.nsg_combo.addItem(display_text, nsg)
        else:
            self.nsg_combo.addItem("暂无安全组", None)

        self.progress_bar.setVisible(False)

        if len(security_groups) == 0:
            self.status_label.setText("暂无可用的网络安全组")
        else:
            self.status_label.setText(f"已加载 {len(security_groups)} 个安全组")

    def on_nsg_changed(self):
        """安全组选择变化"""
        current_nsg = self.nsg_combo.currentData()
        if current_nsg:
            self.add_rule_btn.setEnabled(True)
            self.load_security_rules()
        else:
            self.add_rule_btn.setEnabled(False)

    def load_security_rules(self):
        """加载安全规则"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(50)
        self.status_label.setText("正在加载安全规则...")

        current_nsg = self.nsg_combo.currentData()
        if not current_nsg:
            self.progress_bar.setVisible(False)
            self.status_label.setText("请先选择安全组")
            return

        try:
            # 尝试从原始组件获取真实数据
            if hasattr(self.security_widget, 'auth_info') and self.security_widget.auth_info:
                auth_client = self.security_widget.auth_info.get('auth_client')
                if auth_client:
                    # 调用Azure API获取真实的安全规则
                    resource_group = current_nsg.get('resourceGroup')
                    nsg_name = current_nsg.get('name')
                    if resource_group and nsg_name:
                        rules = auth_client.get_network_security_group_rules(resource_group, nsg_name)
                    else:
                        rules = []
                else:
                    rules = []
            else:
                rules = []

        except Exception as e:
            print(f"获取安全规则失败: {str(e)}")
            rules = []

        self.update_rules_table(rules)
        self.progress_bar.setVisible(False)

        if len(rules) == 0:
            self.status_label.setText("该安全组暂无安全规则")
        else:
            self.status_label.setText(f"已加载 {len(rules)} 条安全规则")

    def update_rules_table(self, rules):
        """更新规则表格"""
        self.rules_table.setRowCount(len(rules))

        for row, rule in enumerate(rules):
            self.rules_table.setItem(row, 0, QTableWidgetItem(rule.get('name', '')))
            self.rules_table.setItem(row, 1, QTableWidgetItem(rule.get('direction', '')))
            self.rules_table.setItem(row, 2, QTableWidgetItem(str(rule.get('priority', ''))))
            self.rules_table.setItem(row, 3, QTableWidgetItem(rule.get('protocol', '')))
            self.rules_table.setItem(row, 4, QTableWidgetItem(rule.get('sourcePortRange', '')))
            self.rules_table.setItem(row, 5, QTableWidgetItem(rule.get('destinationPortRange', '')))
            self.rules_table.setItem(row, 6, QTableWidgetItem(rule.get('sourceAddressPrefix', '')))
            self.rules_table.setItem(row, 7, QTableWidgetItem(rule.get('destinationAddressPrefix', '')))
            self.rules_table.setItem(row, 8, QTableWidgetItem(rule.get('access', '')))

    def on_rule_selection_changed(self):
        """规则选择变化"""
        selected_rows = self.rules_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        self.edit_rule_btn.setEnabled(has_selection)
        self.delete_rule_btn.setEnabled(has_selection)

    def add_security_rule(self):
        """添加安全规则"""
        current_nsg = self.nsg_combo.currentData()
        if not current_nsg:
            QMessageBox.warning(self, "警告", "请先选择网络安全组")
            return

        from ui.dialogs.security_rule_dialog import SecurityRuleDialog
        dialog = SecurityRuleDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            rule_config = dialog.get_rule_config()

            # 显示进度
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(50)
            self.status_label.setText(f"正在添加安全规则 {rule_config['name']}...")
            self.add_rule_btn.setEnabled(False)

            try:
                if hasattr(self.security_widget, 'auth_info') and self.security_widget.auth_info:
                    auth_client = self.security_widget.auth_info.get('auth_client')
                    if auth_client:
                        # 调用Azure API创建安全规则
                        result = auth_client.create_security_rule(
                            current_nsg['resourceGroup'],
                            current_nsg['name'],
                            rule_config
                        )

                        if result['success']:
                            QMessageBox.information(self, "成功", f"安全规则 {rule_config['name']} 添加成功")
                            self.load_security_rules()
                        else:
                            QMessageBox.critical(self, "错误", f"添加安全规则失败: {result['error']}")
                    else:
                        QMessageBox.warning(self, "错误", "认证客户端不可用")
                else:
                    QMessageBox.warning(self, "错误", "未找到认证信息")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"添加安全规则时发生错误: {str(e)}")

            finally:
                self.progress_bar.setVisible(False)
                self.add_rule_btn.setEnabled(True)

    def edit_security_rule(self):
        """编辑安全规则"""
        selected_rows = self.rules_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        current_nsg = self.nsg_combo.currentData()
        if not current_nsg:
            QMessageBox.warning(self, "警告", "请先选择网络安全组")
            return

        row = selected_rows[0].row()
        rule_name = self.rules_table.item(row, 0).text()

        # 检查是否为默认规则
        if "(默认)" in rule_name:
            QMessageBox.warning(self, "警告", "无法编辑默认安全规则")
            return

        # 获取当前规则数据
        rule_data = {
            'name': self.rules_table.item(row, 0).text(),
            'direction': self.rules_table.item(row, 1).text(),
            'priority': self.rules_table.item(row, 2).text(),
            'protocol': self.rules_table.item(row, 3).text(),
            'sourcePortRange': self.rules_table.item(row, 4).text(),
            'destinationPortRange': self.rules_table.item(row, 5).text(),
            'sourceAddressPrefix': self.rules_table.item(row, 6).text(),
            'destinationAddressPrefix': self.rules_table.item(row, 7).text(),
            'access': self.rules_table.item(row, 8).text()
        }

        from ui.dialogs.security_rule_dialog import SecurityRuleDialog
        dialog = SecurityRuleDialog(parent=self, rule_data=rule_data)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            new_rule_config = dialog.get_rule_config()

            # 显示进度
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(50)
            self.status_label.setText(f"正在更新安全规则 {rule_name}...")
            self.edit_rule_btn.setEnabled(False)

            try:
                if hasattr(self.security_widget, 'auth_info') and self.security_widget.auth_info:
                    auth_client = self.security_widget.auth_info.get('auth_client')
                    if auth_client:
                        # 如果规则名称改变了，需要先删除旧规则再创建新规则
                        if new_rule_config['name'] != rule_name:
                            # 删除旧规则
                            delete_result = auth_client.delete_security_rule(
                                current_nsg['resourceGroup'],
                                current_nsg['name'],
                                rule_name
                            )

                            if not delete_result['success']:
                                QMessageBox.critical(self, "错误", f"删除旧规则失败: {delete_result['error']}")
                                return

                        # 创建/更新规则
                        result = auth_client.create_security_rule(
                            current_nsg['resourceGroup'],
                            current_nsg['name'],
                            new_rule_config
                        )

                        if result['success']:
                            QMessageBox.information(self, "成功", f"安全规则更新成功")
                            self.load_security_rules()
                        else:
                            QMessageBox.critical(self, "错误", f"更新安全规则失败: {result['error']}")
                    else:
                        QMessageBox.warning(self, "错误", "认证客户端不可用")
                else:
                    QMessageBox.warning(self, "错误", "未找到认证信息")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"编辑安全规则时发生错误: {str(e)}")

            finally:
                self.progress_bar.setVisible(False)
                self.edit_rule_btn.setEnabled(True)

    def delete_security_rule(self):
        """删除安全规则"""
        selected_rows = self.rules_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        current_nsg = self.nsg_combo.currentData()
        if not current_nsg:
            QMessageBox.warning(self, "警告", "请先选择网络安全组")
            return

        row = selected_rows[0].row()
        rule_name = self.rules_table.item(row, 0).text()

        # 检查是否为默认规则
        if "(默认)" in rule_name:
            QMessageBox.warning(self, "警告", "无法删除默认安全规则")
            return

        reply = QMessageBox.question(
            self, '确认删除',
            f'确定要删除安全规则 "{rule_name}" 吗？\n\n注意：此操作不可撤销！',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 显示进度
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(50)
            self.status_label.setText(f"正在删除安全规则 {rule_name}...")
            self.delete_rule_btn.setEnabled(False)

            try:
                if hasattr(self.security_widget, 'auth_info') and self.security_widget.auth_info:
                    auth_client = self.security_widget.auth_info.get('auth_client')
                    if auth_client:
                        # 调用Azure API删除安全规则
                        result = auth_client.delete_security_rule(
                            current_nsg['resourceGroup'],
                            current_nsg['name'],
                            rule_name
                        )

                        if result['success']:
                            QMessageBox.information(self, "成功", f"安全规则 {rule_name} 删除成功")
                            self.load_security_rules()
                        else:
                            QMessageBox.critical(self, "错误", f"删除安全规则失败: {result['error']}")
                    else:
                        QMessageBox.warning(self, "错误", "认证客户端不可用")
                else:
                    QMessageBox.warning(self, "错误", "未找到认证信息")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除安全规则时发生错误: {str(e)}")

            finally:
                self.progress_bar.setVisible(False)
                self.delete_rule_btn.setEnabled(True)
