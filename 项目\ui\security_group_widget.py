#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全组管理组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QProgressBar, QLabel, QMessageBox, QComboBox,
                             QDialog, QFormLayout, QLineEdit, QSpinBox,
                             QDialogButtonBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont


class SecurityGroupWorker(QThread):
    """安全组工作线程"""
    progress_update = pyqtSignal(int, str)
    data_loaded = pyqtSignal(list)
    operation_complete = pyqtSignal(str)
    operation_failed = pyqtSignal(str)
    
    def __init__(self, auth_client, operation, **kwargs):
        super().__init__()
        self.auth_client = auth_client
        self.operation = operation
        self.kwargs = kwargs
        
    def run(self):
        """执行操作"""
        try:
            if self.operation == 'load_security_groups':
                self.load_security_groups()
            elif self.operation == 'load_rules':
                self.load_security_rules()
            elif self.operation == 'add_rule':
                self.add_security_rule()
            elif self.operation == 'delete_rule':
                self.delete_security_rule()
                
        except Exception as e:
            self.operation_failed.emit(f"操作失败: {str(e)}")
            
    def load_security_groups(self):
        """加载安全组列表"""
        self.progress_update.emit(50, "正在获取网络安全组列表...")
        
        # 这里应该调用Azure API获取安全组
        # 模拟数据
        security_groups = [
            {
                'name': 'default-nsg',
                'resourceGroup': 'test-rg',
                'location': 'East US',
                'id': '/subscriptions/.../default-nsg'
            }
        ]
        
        self.progress_update.emit(100, "安全组列表加载完成")
        self.data_loaded.emit(security_groups)
        
    def load_security_rules(self):
        """加载安全规则"""
        nsg_name = self.kwargs.get('nsg_name')
        self.progress_update.emit(50, f"正在获取 {nsg_name} 的安全规则...")
        
        # 这里应该调用Azure API获取安全规则
        # 模拟数据
        rules = [
            {
                'name': 'AllowSSH',
                'direction': 'Inbound',
                'priority': 1000,
                'protocol': 'TCP',
                'sourcePortRange': '*',
                'destinationPortRange': '22',
                'sourceAddressPrefix': '*',
                'destinationAddressPrefix': '*',
                'access': 'Allow'
            },
            {
                'name': 'AllowHTTP',
                'direction': 'Inbound',
                'priority': 1001,
                'protocol': 'TCP',
                'sourcePortRange': '*',
                'destinationPortRange': '80',
                'sourceAddressPrefix': '*',
                'destinationAddressPrefix': '*',
                'access': 'Allow'
            }
        ]
        
        self.progress_update.emit(100, "安全规则加载完成")
        self.data_loaded.emit(rules)
        
    def add_security_rule(self):
        """添加安全规则"""
        rule_config = self.kwargs.get('rule_config')
        nsg_name = self.kwargs.get('nsg_name')
        
        self.progress_update.emit(50, f"正在添加安全规则到 {nsg_name}...")
        
        # 这里应该调用Azure API添加规则
        import time
        time.sleep(1)  # 模拟操作
        
        self.progress_update.emit(100, "安全规则添加完成")
        self.operation_complete.emit(f"安全规则 {rule_config.get('name')} 已添加")
        
    def delete_security_rule(self):
        """删除安全规则"""
        rule_name = self.kwargs.get('rule_name')
        nsg_name = self.kwargs.get('nsg_name')
        
        self.progress_update.emit(50, f"正在删除安全规则 {rule_name}...")
        
        # 这里应该调用Azure API删除规则
        import time
        time.sleep(1)  # 模拟操作
        
        self.progress_update.emit(100, "安全规则删除完成")
        self.operation_complete.emit(f"安全规则 {rule_name} 已删除")


class SecurityRuleDialog(QDialog):
    """安全规则编辑对话框"""
    
    def __init__(self, rule_data=None, parent=None):
        super().__init__(parent)
        self.rule_data = rule_data
        self.init_ui()
        
        if rule_data:
            self.load_rule_data()
            
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("安全规则配置")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout(self)
        
        # 表单布局
        form_layout = QFormLayout()
        
        # 规则名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入规则名称")
        form_layout.addRow("规则名称:", self.name_edit)
        
        # 方向
        self.direction_combo = QComboBox()
        self.direction_combo.addItems(["Inbound", "Outbound"])
        form_layout.addRow("方向:", self.direction_combo)
        
        # 优先级
        self.priority_spin = QSpinBox()
        self.priority_spin.setRange(100, 4096)
        self.priority_spin.setValue(1000)
        form_layout.addRow("优先级:", self.priority_spin)
        
        # 协议
        self.protocol_combo = QComboBox()
        self.protocol_combo.addItems(["TCP", "UDP", "Any"])
        form_layout.addRow("协议:", self.protocol_combo)
        
        # 源端口范围
        self.source_port_edit = QLineEdit()
        self.source_port_edit.setText("*")
        self.source_port_edit.setPlaceholderText("例如: *, 80, 80-90")
        form_layout.addRow("源端口范围:", self.source_port_edit)
        
        # 目标端口范围
        self.dest_port_edit = QLineEdit()
        self.dest_port_edit.setPlaceholderText("例如: *, 80, 80-90")
        form_layout.addRow("目标端口范围:", self.dest_port_edit)
        
        # 源地址前缀
        self.source_addr_edit = QLineEdit()
        self.source_addr_edit.setText("*")
        self.source_addr_edit.setPlaceholderText("例如: *, 192.168.1.0/24")
        form_layout.addRow("源地址前缀:", self.source_addr_edit)
        
        # 目标地址前缀
        self.dest_addr_edit = QLineEdit()
        self.dest_addr_edit.setText("*")
        self.dest_addr_edit.setPlaceholderText("例如: *, 192.168.1.0/24")
        form_layout.addRow("目标地址前缀:", self.dest_addr_edit)
        
        # 访问权限
        self.access_combo = QComboBox()
        self.access_combo.addItems(["Allow", "Deny"])
        form_layout.addRow("访问权限:", self.access_combo)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
    def load_rule_data(self):
        """加载规则数据"""
        if not self.rule_data:
            return
            
        self.name_edit.setText(self.rule_data.get('name', ''))
        
        direction = self.rule_data.get('direction', 'Inbound')
        index = self.direction_combo.findText(direction)
        if index >= 0:
            self.direction_combo.setCurrentIndex(index)
            
        self.priority_spin.setValue(self.rule_data.get('priority', 1000))
        
        protocol = self.rule_data.get('protocol', 'TCP')
        index = self.protocol_combo.findText(protocol)
        if index >= 0:
            self.protocol_combo.setCurrentIndex(index)
            
        self.source_port_edit.setText(self.rule_data.get('sourcePortRange', '*'))
        self.dest_port_edit.setText(self.rule_data.get('destinationPortRange', ''))
        self.source_addr_edit.setText(self.rule_data.get('sourceAddressPrefix', '*'))
        self.dest_addr_edit.setText(self.rule_data.get('destinationAddressPrefix', '*'))
        
        access = self.rule_data.get('access', 'Allow')
        index = self.access_combo.findText(access)
        if index >= 0:
            self.access_combo.setCurrentIndex(index)
            
    def get_rule_config(self):
        """获取规则配置"""
        return {
            'name': self.name_edit.text().strip(),
            'direction': self.direction_combo.currentText(),
            'priority': self.priority_spin.value(),
            'protocol': self.protocol_combo.currentText(),
            'sourcePortRange': self.source_port_edit.text().strip(),
            'destinationPortRange': self.dest_port_edit.text().strip(),
            'sourceAddressPrefix': self.source_addr_edit.text().strip(),
            'destinationAddressPrefix': self.dest_addr_edit.text().strip(),
            'access': self.access_combo.currentText()
        }


class SecurityGroupWidget(QWidget):
    """安全组管理组件"""
    
    def __init__(self):
        super().__init__()
        self.auth_info = None
        self.security_groups = []
        self.current_rules = []
        self.current_nsg = None
        self.init_ui()
        self.setEnabled(False)  # 初始状态禁用
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 安全组选择区域
        nsg_layout = QHBoxLayout()
        
        nsg_label = QLabel("选择安全组:")
        nsg_layout.addWidget(nsg_label)
        
        self.nsg_combo = QComboBox()
        self.nsg_combo.currentTextChanged.connect(self.on_nsg_changed)
        nsg_layout.addWidget(self.nsg_combo)
        
        self.refresh_nsg_btn = QPushButton("刷新")
        self.refresh_nsg_btn.clicked.connect(self.refresh_security_groups)
        nsg_layout.addWidget(self.refresh_nsg_btn)
        
        nsg_layout.addStretch()
        layout.addLayout(nsg_layout)
        
        # 操作按钮区域
        button_layout = QHBoxLayout()
        
        self.add_rule_btn = QPushButton("添加规则")
        self.add_rule_btn.clicked.connect(self.add_security_rule)
        self.add_rule_btn.setEnabled(False)
        button_layout.addWidget(self.add_rule_btn)
        
        self.edit_rule_btn = QPushButton("编辑规则")
        self.edit_rule_btn.clicked.connect(self.edit_security_rule)
        self.edit_rule_btn.setEnabled(False)
        button_layout.addWidget(self.edit_rule_btn)
        
        self.delete_rule_btn = QPushButton("删除规则")
        self.delete_rule_btn.clicked.connect(self.delete_security_rule)
        self.delete_rule_btn.setEnabled(False)
        button_layout.addWidget(self.delete_rule_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("请选择安全组")
        layout.addWidget(self.status_label)
        
        # 规则表格
        rules_label = QLabel("安全规则:")
        rules_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(rules_label)
        
        self.rules_table = QTableWidget()
        self.rules_table.setColumnCount(9)
        self.rules_table.setHorizontalHeaderLabels([
            "名称", "方向", "优先级", "协议", "源端口", "目标端口", "源地址", "目标地址", "访问权限"
        ])
        
        # 设置表格属性
        header = self.rules_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.rules_table.setAlternatingRowColors(True)
        self.rules_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.rules_table.itemSelectionChanged.connect(self.on_rule_selection_changed)
        
        layout.addWidget(self.rules_table)
        
    def set_auth_info(self, auth_info):
        """设置认证信息"""
        self.auth_info = auth_info
        self.refresh_security_groups()
        
    def refresh_security_groups(self):
        """刷新安全组列表"""
        if not self.auth_info or not self.auth_info.get('auth_client'):
            return
            
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.refresh_nsg_btn.setEnabled(False)
        self.status_label.setText("正在加载安全组...")
        
        self.worker = SecurityGroupWorker(
            self.auth_info['auth_client'],
            'load_security_groups'
        )
        self.worker.progress_update.connect(self.on_progress_update)
        self.worker.data_loaded.connect(self.on_security_groups_loaded)
        self.worker.operation_failed.connect(self.on_operation_failed)
        self.worker.start()
        
    def on_nsg_changed(self):
        """安全组选择变化"""
        current_nsg = self.nsg_combo.currentData()
        if current_nsg:
            self.current_nsg = current_nsg
            self.load_security_rules()
            self.add_rule_btn.setEnabled(True)
        else:
            self.current_nsg = None
            self.add_rule_btn.setEnabled(False)
            
    def load_security_rules(self):
        """加载安全规则"""
        if not self.current_nsg:
            return
            
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在加载安全规则...")
        
        self.worker = SecurityGroupWorker(
            self.auth_info['auth_client'],
            'load_rules',
            nsg_name=self.current_nsg['name']
        )
        self.worker.progress_update.connect(self.on_progress_update)
        self.worker.data_loaded.connect(self.on_rules_loaded)
        self.worker.operation_failed.connect(self.on_operation_failed)
        self.worker.start()
        
    def on_progress_update(self, value, message):
        """进度更新"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        
    def on_security_groups_loaded(self, data):
        """安全组加载完成"""
        self.progress_bar.setVisible(False)
        self.refresh_nsg_btn.setEnabled(True)
        self.security_groups = data
        
        self.nsg_combo.clear()
        for nsg in self.security_groups:
            display_text = f"{nsg.get('name')} ({nsg.get('resourceGroup')})"
            self.nsg_combo.addItem(display_text, nsg)
            
        self.status_label.setText(f"已加载 {len(self.security_groups)} 个安全组")
        
    def on_rules_loaded(self, data):
        """规则加载完成"""
        self.progress_bar.setVisible(False)
        self.current_rules = data
        self.update_rules_table()
        self.status_label.setText(f"已加载 {len(self.current_rules)} 条安全规则")
        
    def update_rules_table(self):
        """更新规则表格"""
        self.rules_table.setRowCount(len(self.current_rules))
        
        for row, rule in enumerate(self.current_rules):
            self.rules_table.setItem(row, 0, QTableWidgetItem(rule.get('name', '')))
            self.rules_table.setItem(row, 1, QTableWidgetItem(rule.get('direction', '')))
            self.rules_table.setItem(row, 2, QTableWidgetItem(str(rule.get('priority', ''))))
            self.rules_table.setItem(row, 3, QTableWidgetItem(rule.get('protocol', '')))
            self.rules_table.setItem(row, 4, QTableWidgetItem(rule.get('sourcePortRange', '')))
            self.rules_table.setItem(row, 5, QTableWidgetItem(rule.get('destinationPortRange', '')))
            self.rules_table.setItem(row, 6, QTableWidgetItem(rule.get('sourceAddressPrefix', '')))
            self.rules_table.setItem(row, 7, QTableWidgetItem(rule.get('destinationAddressPrefix', '')))
            self.rules_table.setItem(row, 8, QTableWidgetItem(rule.get('access', '')))
            
    def on_rule_selection_changed(self):
        """规则选择变化"""
        selected_rows = self.rules_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0
        
        self.edit_rule_btn.setEnabled(has_selection)
        self.delete_rule_btn.setEnabled(has_selection)
        
    def add_security_rule(self):
        """添加安全规则"""
        dialog = SecurityRuleDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            rule_config = dialog.get_rule_config()
            
            if not rule_config['name']:
                QMessageBox.warning(self, "警告", "请输入规则名称")
                return
                
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.add_rule_btn.setEnabled(False)
            
            self.worker = SecurityGroupWorker(
                self.auth_info['auth_client'],
                'add_rule',
                nsg_name=self.current_nsg['name'],
                rule_config=rule_config
            )
            self.worker.progress_update.connect(self.on_progress_update)
            self.worker.operation_complete.connect(self.on_rule_operation_complete)
            self.worker.operation_failed.connect(self.on_operation_failed)
            self.worker.start()
            
    def edit_security_rule(self):
        """编辑安全规则"""
        selected_rows = self.rules_table.selectionModel().selectedRows()
        if not selected_rows:
            return
            
        row = selected_rows[0].row()
        rule_data = self.current_rules[row]
        
        dialog = SecurityRuleDialog(rule_data, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            rule_config = dialog.get_rule_config()
            
            # 这里应该调用编辑规则的API
            QMessageBox.information(self, "提示", "规则编辑功能待实现")
            
    def delete_security_rule(self):
        """删除安全规则"""
        selected_rows = self.rules_table.selectionModel().selectedRows()
        if not selected_rows:
            return
            
        row = selected_rows[0].row()
        rule_name = self.current_rules[row]['name']
        
        reply = QMessageBox.question(
            self, '确认删除', 
            f'确定要删除安全规则 "{rule_name}" 吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.delete_rule_btn.setEnabled(False)
            
            self.worker = SecurityGroupWorker(
                self.auth_info['auth_client'],
                'delete_rule',
                nsg_name=self.current_nsg['name'],
                rule_name=rule_name
            )
            self.worker.progress_update.connect(self.on_progress_update)
            self.worker.operation_complete.connect(self.on_rule_operation_complete)
            self.worker.operation_failed.connect(self.on_operation_failed)
            self.worker.start()
            
    def on_rule_operation_complete(self, message):
        """规则操作完成"""
        self.progress_bar.setVisible(False)
        self.add_rule_btn.setEnabled(True)
        self.delete_rule_btn.setEnabled(True)
        
        QMessageBox.information(self, "操作成功", message)
        
        # 重新加载规则
        self.load_security_rules()
        
    def on_operation_failed(self, error_msg):
        """操作失败"""
        self.progress_bar.setVisible(False)
        self.refresh_nsg_btn.setEnabled(True)
        self.add_rule_btn.setEnabled(True)
        self.delete_rule_btn.setEnabled(True)
        self.status_label.setText("操作失败")
        
        QMessageBox.critical(self, "错误", error_msg)
