#!/usr/bin/env python3
"""
Command Executor for Ubuntu Terminal
Handles real command execution with proper process management
"""

import os
import sys
import subprocess
import threading
import signal
from PyQt5.QtCore import QObject, pyqtSignal, QThread, QProcess, QTimer
from PyQt5.QtWidgets import QApplication


class CommandExecutor(QObject):
    """Executes shell commands and captures output"""
    
    # Signals for communication with UI
    output_ready = pyqtSignal(str)
    error_ready = pyqtSignal(str)
    finished = pyqtSignal(int)  # exit code
    
    def __init__(self):
        super().__init__()
        self.current_process = None
        self.is_running = False
        
    def execute_command(self, command, working_directory):
        """Execute a shell command in the specified directory"""
        if self.is_running:
            self.error_ready.emit("Another command is already running.\n")
            return
            
        # Start command execution in a separate thread
        self.command_thread = CommandThread(command, working_directory)
        self.command_thread.output_ready.connect(self.output_ready.emit)
        self.command_thread.error_ready.connect(self.error_ready.emit)
        self.command_thread.finished.connect(self._on_command_finished)
        
        self.is_running = True
        self.command_thread.start()
        
    def _on_command_finished(self, exit_code):
        """Handle command completion"""
        self.is_running = False
        self.finished.emit(exit_code)
        
    def terminate_current_command(self):
        """Terminate the currently running command"""
        if self.is_running and hasattr(self, 'command_thread'):
            self.command_thread.terminate_process()


class CommandThread(QThread):
    """Thread for executing commands without blocking the UI"""
    
    output_ready = pyqtSignal(str)
    error_ready = pyqtSignal(str)
    finished = pyqtSignal(int)
    
    def __init__(self, command, working_directory):
        super().__init__()
        self.command = command
        self.working_directory = working_directory
        self.process = None
        
    def run(self):
        """Execute the command in this thread"""
        try:
            # Determine shell based on platform
            if sys.platform.startswith('win'):
                # On Windows, try to use WSL if available
                if self._is_wsl_available():
                    self._execute_wsl_command()
                else:
                    # Check if this is a Linux command that won't work on Windows
                    if self._is_linux_command(self.command):
                        self._show_wsl_installation_help()
                    else:
                        self._execute_windows_command()
            else:
                # On Linux/Unix systems
                self._execute_unix_command()
                
        except Exception as e:
            self.error_ready.emit(f"Error executing command: {str(e)}\n")
            self.finished.emit(1)
            
    def _is_wsl_available(self):
        """Check if WSL is available on Windows"""
        try:
            # Try to run a simple command in WSL
            result = subprocess.run(['wsl', 'echo', 'test'],
                                  capture_output=True,
                                  text=True,
                                  timeout=5,
                                  encoding='utf-8',
                                  errors='replace')

            # If the command succeeds and returns 'test', WSL is working
            return result.returncode == 0 and 'test' in result.stdout
        except (subprocess.TimeoutExpired, FileNotFoundError, UnicodeDecodeError):
            return False

    def _is_linux_command(self, command):
        """Check if the command is a typical Linux command"""
        linux_commands = {
            'ls', 'pwd', 'cd', 'mkdir', 'rmdir', 'rm', 'cp', 'mv', 'cat', 'less', 'more',
            'grep', 'find', 'which', 'whereis', 'man', 'info', 'top', 'ps', 'kill', 'killall',
            'chmod', 'chown', 'chgrp', 'sudo', 'su', 'whoami', 'id', 'groups', 'passwd',
            'ssh', 'scp', 'rsync', 'wget', 'curl', 'ping', 'netstat', 'ifconfig', 'ip',
            'tar', 'gzip', 'gunzip', 'zip', 'unzip', 'df', 'du', 'free', 'mount', 'umount',
            'systemctl', 'service', 'crontab', 'history', 'alias', 'unalias', 'export',
            'env', 'printenv', 'source', 'bash', 'sh', 'zsh', 'fish', 'nano', 'vim', 'emacs',
            'apt', 'apt-get', 'yum', 'dnf', 'pacman', 'zypper', 'emerge', 'brew',
            'git', 'svn', 'hg', 'bzr', 'make', 'cmake', 'gcc', 'g++', 'clang', 'python3',
            'node', 'npm', 'yarn', 'pip', 'pip3', 'docker', 'kubectl', 'helm', 'terraform'
        }

        # Extract the first word (command name) from the command
        cmd_parts = command.strip().split()
        if cmd_parts:
            cmd_name = cmd_parts[0]
            return cmd_name in linux_commands
        return False

    def _show_wsl_installation_help(self):
        """Show help message for installing WSL"""
        help_message = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                           需要 Linux 环境                                    ║
╠══════════════════════════════════════════════════════════════════════════════╣
║ 您尝试执行的是 Linux 命令，但当前系统是 Windows。                            ║
║ 要使用 Linux 命令，您需要安装 WSL (Windows Subsystem for Linux)。           ║
║                                                                              ║
║ 安装步骤：                                                                   ║
║ 1. 以管理员身份打开 PowerShell 或命令提示符                                  ║
║ 2. 运行命令: wsl --install                                                   ║
║ 3. 重启计算机                                                                ║
║ 4. 重新启动此应用程序                                                        ║
║                                                                              ║
║ 或者，您可以使用等效的 Windows 命令：                                        ║
║ • ls → dir                                                                   ║
║ • pwd → cd                                                                   ║
║ • cat → type                                                                 ║
║ • rm → del                                                                   ║
║ • cp → copy                                                                  ║
║ • mv → move                                                                  ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
        self.output_ready.emit(help_message)
        self.finished.emit(1)
            
    def _execute_wsl_command(self):
        """Execute command using WSL on Windows"""
        # Convert Windows path to WSL path if needed
        wsl_path = self._convert_to_wsl_path(self.working_directory)

        # Prepare WSL command
        wsl_command = f"cd '{wsl_path}' && {self.command}"

        try:
            self.process = subprocess.Popen(
                ['wsl', 'bash', '-c', wsl_command],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                encoding='utf-8',
                errors='replace'
            )

            self._read_process_output()

        except Exception as e:
            self.error_ready.emit(f"WSL execution error: {str(e)}\n")
            self.finished.emit(1)
            
    def _execute_windows_command(self):
        """Execute command using Windows Command Prompt"""
        try:
            # Try to use chcp 65001 to set UTF-8 encoding
            full_command = f"chcp 65001 >nul 2>&1 && {self.command}"

            self.process = subprocess.Popen(
                full_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.working_directory,
                bufsize=1,
                universal_newlines=True,
                encoding='utf-8',
                errors='replace'
            )

            self._read_process_output()

        except Exception as e:
            self.error_ready.emit(f"Windows execution error: {str(e)}\n")
            self.finished.emit(1)
            
    def _execute_unix_command(self):
        """Execute command on Unix/Linux systems"""
        try:
            self.process = subprocess.Popen(
                self.command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.working_directory,
                bufsize=1,
                universal_newlines=True,
                encoding='utf-8',
                errors='replace',
                preexec_fn=os.setsid  # Create new process group
            )

            self._read_process_output()

        except Exception as e:
            self.error_ready.emit(f"Unix execution error: {str(e)}\n")
            self.finished.emit(1)
            
    def _read_process_output(self):
        """Read process output in real-time"""
        if not self.process:
            return
            
        # Read stdout and stderr simultaneously
        stdout_thread = threading.Thread(target=self._read_stdout)
        stderr_thread = threading.Thread(target=self._read_stderr)
        
        stdout_thread.daemon = True
        stderr_thread.daemon = True
        
        stdout_thread.start()
        stderr_thread.start()
        
        # Wait for process to complete
        exit_code = self.process.wait()
        
        # Wait for output threads to finish
        stdout_thread.join(timeout=1)
        stderr_thread.join(timeout=1)
        
        self.finished.emit(exit_code)
        
    def _read_stdout(self):
        """Read stdout in a separate thread"""
        try:
            while True:
                line = self.process.stdout.readline()
                if not line:
                    break
                self.output_ready.emit(line)
        except Exception:
            pass
            
    def _read_stderr(self):
        """Read stderr in a separate thread"""
        try:
            while True:
                line = self.process.stderr.readline()
                if not line:
                    break
                self.error_ready.emit(line)
        except Exception:
            pass
            
    def _convert_to_wsl_path(self, windows_path):
        """Convert Windows path to WSL path"""
        if windows_path.startswith('C:'):
            return '/mnt/c' + windows_path[2:].replace('\\', '/')
        elif windows_path.startswith('D:'):
            return '/mnt/d' + windows_path[2:].replace('\\', '/')
        else:
            # For other drives, try to convert
            drive = windows_path[0].lower()
            return f'/mnt/{drive}' + windows_path[2:].replace('\\', '/')
            
    def terminate_process(self):
        """Terminate the running process"""
        if self.process and self.process.poll() is None:
            try:
                if sys.platform.startswith('win'):
                    self.process.terminate()
                else:
                    # On Unix, terminate the entire process group
                    os.killpg(os.getpgid(self.process.pid), signal.SIGTERM)
            except (ProcessLookupError, PermissionError):
                pass


class InteractiveCommandExecutor(QObject):
    """Executor for interactive commands that require continuous I/O"""
    
    output_ready = pyqtSignal(str)
    error_ready = pyqtSignal(str)
    finished = pyqtSignal(int)
    
    def __init__(self):
        super().__init__()
        self.process = None
        
    def start_interactive_command(self, command, working_directory):
        """Start an interactive command"""
        try:
            if sys.platform.startswith('win'):
                # Use WSL for interactive commands on Windows
                wsl_command = f"cd '{working_directory}' && {command}"
                self.process = subprocess.Popen(
                    ['wsl', 'bash', '-c', wsl_command],
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=0,
                    encoding='utf-8',
                    errors='replace'
                )
            else:
                self.process = subprocess.Popen(
                    command,
                    shell=True,
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=working_directory,
                    bufsize=0,
                    encoding='utf-8',
                    errors='replace',
                    preexec_fn=os.setsid
                )

            # Start output monitoring
            self._start_output_monitoring()

        except Exception as e:
            self.error_ready.emit(f"Failed to start interactive command: {str(e)}\n")
            
    def send_input(self, text):
        """Send input to the interactive process"""
        if self.process and self.process.stdin:
            try:
                self.process.stdin.write(text)
                self.process.stdin.flush()
            except Exception as e:
                self.error_ready.emit(f"Failed to send input: {str(e)}\n")
                
    def _start_output_monitoring(self):
        """Start monitoring process output"""
        if not self.process:
            return
            
        # Monitor stdout
        stdout_thread = threading.Thread(target=self._monitor_stdout)
        stdout_thread.daemon = True
        stdout_thread.start()
        
        # Monitor stderr
        stderr_thread = threading.Thread(target=self._monitor_stderr)
        stderr_thread.daemon = True
        stderr_thread.start()
        
        # Monitor process completion
        completion_thread = threading.Thread(target=self._monitor_completion)
        completion_thread.daemon = True
        completion_thread.start()
        
    def _monitor_stdout(self):
        """Monitor stdout output"""
        try:
            while self.process and self.process.poll() is None:
                line = self.process.stdout.readline()
                if line:
                    self.output_ready.emit(line)
        except Exception:
            pass
            
    def _monitor_stderr(self):
        """Monitor stderr output"""
        try:
            while self.process and self.process.poll() is None:
                line = self.process.stderr.readline()
                if line:
                    self.error_ready.emit(line)
        except Exception:
            pass
            
    def _monitor_completion(self):
        """Monitor process completion"""
        if self.process:
            exit_code = self.process.wait()
            self.finished.emit(exit_code)
            
    def terminate(self):
        """Terminate the interactive process"""
        if self.process and self.process.poll() is None:
            try:
                if sys.platform.startswith('win'):
                    self.process.terminate()
                else:
                    os.killpg(os.getpgid(self.process.pid), signal.SIGTERM)
            except (ProcessLookupError, PermissionError):
                pass
