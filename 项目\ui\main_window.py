#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口UI类
"""

import sys
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QSplitter, QTextEdit, QGroupBox, QPushButton,
                             QLabel, QLineEdit, QComboBox, QProgressBar,
                             QScrollArea, QFrame, QMessageBox, QApplication)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon

from ui.auth_widget import AuthWidget
from ui.vm_management_widget import VMManagementWidget
from ui.vm_creation_widget import VMCreationWidget
from ui.security_group_widget import SecurityGroupWidget
from ui.utils import delayed_execution, with_loading_cursor
from utils.logger import Logger


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.logger = Logger()
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("Azure管理工具 v1.0")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建认证区域
        self.auth_widget = AuthWidget()
        main_layout.addWidget(self.auth_widget)
        
        # 创建水平分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建左侧功能区域
        self.create_left_panel(splitter)
        
        # 创建右侧日志区域
        self.create_right_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([800, 600])
        
        # 创建状态栏
        self.statusBar().showMessage("就绪")
        
    def create_left_panel(self, parent):
        """创建左侧功能面板"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(10)

        # 标题
        title_label = QLabel("Azure 管理功能")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        left_layout.addWidget(title_label)

        # 虚拟机管理按钮组
        vm_group = QGroupBox("虚拟机管理")
        vm_layout = QVBoxLayout(vm_group)
        vm_layout.setSpacing(5)

        self.vm_list_btn = QPushButton("查看虚拟机列表")
        self.vm_list_btn.clicked.connect(self._handle_vm_list_click)
        vm_layout.addWidget(self.vm_list_btn)

        self.resource_group_btn = QPushButton("管理资源组")
        self.resource_group_btn.clicked.connect(self._handle_resource_group_click)
        vm_layout.addWidget(self.resource_group_btn)

        left_layout.addWidget(vm_group)

        # 虚拟机创建按钮组
        create_group = QGroupBox("创建资源")
        create_layout = QVBoxLayout(create_group)
        create_layout.setSpacing(5)

        self.create_vm_btn = QPushButton("创建虚拟机")
        self.create_vm_btn.clicked.connect(self._handle_create_vm_click)
        create_layout.addWidget(self.create_vm_btn)

        left_layout.addWidget(create_group)

        # 网络安全组按钮组
        security_group = QGroupBox("网络安全")
        security_layout = QVBoxLayout(security_group)
        security_layout.setSpacing(5)

        self.security_group_btn = QPushButton("管理安全组")
        self.security_group_btn.clicked.connect(self._handle_security_group_click)
        security_layout.addWidget(self.security_group_btn)

        left_layout.addWidget(security_group)

        # 工具按钮组
        tools_group = QGroupBox("工具")
        tools_layout = QVBoxLayout(tools_group)
        tools_layout.setSpacing(5)

        self.refresh_all_btn = QPushButton("刷新所有数据")
        self.refresh_all_btn.clicked.connect(self._handle_refresh_all_click)
        tools_layout.addWidget(self.refresh_all_btn)

        self.queue_manager_btn = QPushButton("创建队列管理")
        self.queue_manager_btn.clicked.connect(self._handle_queue_manager_click)
        tools_layout.addWidget(self.queue_manager_btn)

        left_layout.addWidget(tools_group)

        # 添加弹性空间
        left_layout.addStretch()

        # 初始状态禁用所有功能按钮
        self.set_function_buttons_enabled(False)

        parent.addWidget(left_widget)
        
    def create_right_panel(self, parent):
        """创建右侧日志面板"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(5, 5, 5, 5)
        right_layout.setSpacing(10)
        
        # 日志标题
        log_label = QLabel("操作日志")
        log_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        right_layout.addWidget(log_label)
        
        # 日志文本区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 5px;
            }
        """)
        right_layout.addWidget(self.log_text)
        
        # 清除日志按钮
        clear_log_btn = QPushButton("清除日志")
        clear_log_btn.clicked.connect(self.clear_log)
        right_layout.addWidget(clear_log_btn)
        
        parent.addWidget(right_widget)

    def set_function_buttons_enabled(self, enabled):
        """设置功能按钮启用状态"""
        self.vm_list_btn.setEnabled(enabled)
        self.resource_group_btn.setEnabled(enabled)
        self.create_vm_btn.setEnabled(enabled)
        self.security_group_btn.setEnabled(enabled)
        self.refresh_all_btn.setEnabled(enabled)

    def setup_connections(self):
        """设置信号连接"""
        # 连接认证信号
        self.auth_widget.auth_success.connect(self.on_auth_success)
        self.auth_widget.auth_failed.connect(self.on_auth_failed)
        
        # 连接日志信号
        self.logger.log_signal.connect(self.append_log)
        
    def on_auth_success(self, subscription_info):
        """认证成功处理"""
        self.append_log(f"认证成功: {subscription_info.get('displayName', 'Unknown')}")
        self.statusBar().showMessage("已连接到Azure")

        # 启用功能按钮
        self.set_function_buttons_enabled(True)

        # 创建组件实例（延迟创建）
        self.vm_management_widget = VMManagementWidget()
        self.vm_creation_widget = VMCreationWidget()
        self.security_group_widget = SecurityGroupWidget()

        # 传递认证信息给各个组件
        auth_info = self.auth_widget.get_auth_info()
        self.vm_management_widget.set_auth_info(auth_info)
        self.vm_creation_widget.set_auth_info(auth_info)
        self.security_group_widget.set_auth_info(auth_info)
        
    def on_auth_failed(self, error_msg):
        """认证失败处理"""
        self.append_log(f"认证失败: {error_msg}")
        self.statusBar().showMessage("认证失败")

        # 禁用功能按钮
        self.set_function_buttons_enabled(False)
        
    def append_log(self, message):
        """添加日志"""
        self.log_text.append(f"[{self.logger.get_timestamp()}] {message}")
        
    def clear_log(self):
        """清除日志"""
        self.log_text.clear()
        self.append_log("日志已清除")

    def _handle_vm_list_click(self):
        """处理虚拟机列表按钮点击"""
        self.vm_list_btn.setEnabled(False)
        self.vm_list_btn.setText("正在打开...")
        QTimer.singleShot(50, self._restore_vm_list_button)
        self.show_vm_management_dialog()

    def _handle_resource_group_click(self):
        """处理资源组按钮点击"""
        self.resource_group_btn.setEnabled(False)
        self.resource_group_btn.setText("正在打开...")
        QTimer.singleShot(50, self._restore_resource_group_button)
        self.show_resource_group_dialog()

    def _handle_create_vm_click(self):
        """处理创建虚拟机按钮点击"""
        self.create_vm_btn.setEnabled(False)
        self.create_vm_btn.setText("正在打开...")
        QTimer.singleShot(50, self._restore_create_vm_button)
        self.show_vm_creation_dialog()

    def _handle_security_group_click(self):
        """处理安全组按钮点击"""
        self.security_group_btn.setEnabled(False)
        self.security_group_btn.setText("正在打开...")
        QTimer.singleShot(50, self._restore_security_group_button)
        self.show_security_group_dialog()

    def _handle_refresh_all_click(self):
        """处理刷新所有数据按钮点击"""
        self.refresh_all_btn.setEnabled(False)
        self.refresh_all_btn.setText("正在刷新...")
        QTimer.singleShot(50, self._restore_refresh_all_button)
        self.refresh_all_data()

    def _handle_queue_manager_click(self):
        """处理创建队列管理按钮点击"""
        self.queue_manager_btn.setEnabled(False)
        self.queue_manager_btn.setText("正在打开...")
        QTimer.singleShot(50, self._restore_queue_manager_button)
        self.show_queue_manager_dialog()

    def _restore_vm_list_button(self):
        """恢复虚拟机列表按钮"""
        QTimer.singleShot(2000, lambda: (
            self.vm_list_btn.setEnabled(True),
            self.vm_list_btn.setText("查看虚拟机列表")
        ))

    def _restore_resource_group_button(self):
        """恢复资源组按钮"""
        QTimer.singleShot(2000, lambda: (
            self.resource_group_btn.setEnabled(True),
            self.resource_group_btn.setText("管理资源组")
        ))

    def _restore_create_vm_button(self):
        """恢复创建虚拟机按钮"""
        QTimer.singleShot(2000, lambda: (
            self.create_vm_btn.setEnabled(True),
            self.create_vm_btn.setText("创建虚拟机")
        ))

    def _restore_security_group_button(self):
        """恢复安全组按钮"""
        QTimer.singleShot(2000, lambda: (
            self.security_group_btn.setEnabled(True),
            self.security_group_btn.setText("管理安全组")
        ))

    def _restore_refresh_all_button(self):
        """恢复刷新所有数据按钮"""
        QTimer.singleShot(3000, lambda: (
            self.refresh_all_btn.setEnabled(True),
            self.refresh_all_btn.setText("刷新所有数据")
        ))

    def _restore_queue_manager_button(self):
        """恢复创建队列管理按钮"""
        QTimer.singleShot(2000, lambda: (
            self.queue_manager_btn.setEnabled(True),
            self.queue_manager_btn.setText("创建队列管理")
        ))

    @delayed_execution(50)
    def show_queue_manager_dialog(self):
        """显示创建队列管理对话框"""
        try:
            if not hasattr(self, 'vm_management_widget'):
                QMessageBox.warning(self, "警告", "请先完成Azure认证")
                return

            # 获取认证客户端
            auth_client = self.vm_management_widget.auth_info.get('auth_client')
            if not auth_client:
                QMessageBox.warning(self, "错误", "认证客户端不可用")
                return

            # 创建或获取全局队列管理器
            if not hasattr(self, 'queue_manager'):
                self.append_log("正在初始化虚拟机创建队列管理器...")
                from azure_client.vm_creation_queue import VMCreationQueue
                self.queue_manager = VMCreationQueue(auth_client, max_workers=40)  # 增加并发数
                self.append_log("队列管理器初始化完成（支持40个并发任务）")

            # 显示队列管理对话框
            from ui.dialogs.vm_queue_dialog import VMQueueDialog

            if not hasattr(self, 'queue_dialog') or not self.queue_dialog.isVisible():
                self.append_log("正在打开队列管理对话框...")
                self.queue_dialog = VMQueueDialog(self.queue_manager, self)
                self.queue_dialog.show()
                self.append_log("队列管理对话框已打开")
                # 立即刷新数据
                QTimer.singleShot(200, self.queue_dialog.refresh_data)
            else:
                # 如果对话框已经打开，刷新数据并激活窗口
                self.queue_dialog.refresh_data()
                self.queue_dialog.raise_()
                self.queue_dialog.activateWindow()

        except Exception as e:
            error_msg = f"打开队列管理对话框失败: {str(e)}"
            self.append_log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    @delayed_execution(50)
    def show_vm_management_dialog(self):
        """显示虚拟机管理对话框"""
        if not hasattr(self, 'vm_management_widget'):
            QMessageBox.warning(self, "警告", "请先完成Azure认证")
            return

        from ui.dialogs.vm_management_dialog import VMManagementDialog
        dialog = VMManagementDialog(self.vm_management_widget, self)
        dialog.exec()

    @delayed_execution(50)
    def show_resource_group_dialog(self):
        """显示资源组管理对话框"""
        if not hasattr(self, 'vm_management_widget'):
            QMessageBox.warning(self, "警告", "请先完成Azure认证")
            return

        from ui.dialogs.resource_group_dialog import ResourceGroupDialog
        dialog = ResourceGroupDialog(self.vm_management_widget, self)
        dialog.exec()

    @delayed_execution(50)
    def show_vm_creation_dialog(self):
        """显示虚拟机创建对话框"""
        if not hasattr(self, 'vm_creation_widget'):
            QMessageBox.warning(self, "警告", "请先完成Azure认证")
            return

        from ui.dialogs.vm_creation_dialog import VMCreationDialog
        dialog = VMCreationDialog(self.vm_creation_widget, self)
        dialog.exec()

    @delayed_execution(50)
    def show_security_group_dialog(self):
        """显示安全组管理对话框"""
        if not hasattr(self, 'security_group_widget'):
            QMessageBox.warning(self, "警告", "请先完成Azure认证")
            return

        from ui.dialogs.security_group_dialog import SecurityGroupDialog
        dialog = SecurityGroupDialog(self.security_group_widget, self)
        dialog.exec()

    def refresh_all_data(self):
        """刷新所有数据"""
        if not hasattr(self, 'vm_management_widget'):
            QMessageBox.warning(self, "警告", "请先完成Azure认证")
            return

        # 立即更新按钮状态
        self.refresh_all_btn.setEnabled(False)
        self.refresh_all_btn.setText("正在刷新...")

        self.append_log("开始刷新所有数据...")

        # 使用定时器延迟执行刷新操作，避免阻塞UI
        from PyQt6.QtCore import QTimer
        QTimer.singleShot(100, self._do_refresh_all_data)

    def _do_refresh_all_data(self):
        """执行实际的数据刷新"""
        try:
            # 刷新虚拟机数据
            if hasattr(self, 'vm_management_widget'):
                self.vm_management_widget.refresh_data()

            self.append_log("数据刷新完成")
        except Exception as e:
            self.append_log(f"数据刷新失败: {str(e)}")
        finally:
            # 恢复按钮状态
            self.refresh_all_btn.setEnabled(True)
            self.refresh_all_btn.setText("刷新所有数据")
        
    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(self, '确认退出', 
                                   '确定要退出Azure管理工具吗？',
                                   QMessageBox.StandardButton.Yes | 
                                   QMessageBox.StandardButton.No,
                                   QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            event.accept()
        else:
            event.ignore()
