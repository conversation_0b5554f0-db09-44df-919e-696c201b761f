#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟机创建组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QComboBox, QPushButton, QCheckBox,
                             QSpinBox, QProgressBar, QLabel, QMessageBox,
                             QGroupBox, QScrollArea)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont


class VMCreationWorker(QThread):
    """虚拟机创建工作线程"""
    progress_update = pyqtSignal(int, str)
    operation_complete = pyqtSignal(str)
    operation_failed = pyqtSignal(str)
    data_loaded = pyqtSignal(str, list)  # 数据类型, 数据列表
    
    def __init__(self, auth_client, operation, **kwargs):
        super().__init__()
        self.auth_client = auth_client
        self.operation = operation
        self.kwargs = kwargs
        
    def run(self):
        """执行操作"""
        try:
            if self.operation == 'load_locations':
                self.load_locations()
            elif self.operation == 'load_vm_sizes':
                self.load_vm_sizes()
            elif self.operation == 'load_vm_images':
                self.load_vm_images()
            elif self.operation == 'create_vm':
                self.create_virtual_machine()
                
        except Exception as e:
            self.operation_failed.emit(f"操作失败: {str(e)}")
            
    def load_locations(self):
        """加载位置列表"""
        self.progress_update.emit(50, "正在获取Azure区域列表...")
        locations = self.auth_client.get_locations()
        self.progress_update.emit(100, "区域列表加载完成")
        self.data_loaded.emit('locations', locations)
        
    def load_vm_sizes(self):
        """加载虚拟机规格"""
        location = self.kwargs.get('location')
        self.progress_update.emit(50, f"正在获取 {location} 的虚拟机规格...")
        vm_sizes = self.auth_client.get_vm_sizes(location)
        self.progress_update.emit(100, "虚拟机规格加载完成")
        self.data_loaded.emit('vm_sizes', vm_sizes)
        
    def load_vm_images(self):
        """加载虚拟机镜像"""
        location = self.kwargs.get('location')
        get_all_images = self.kwargs.get('get_all_images', False)

        if get_all_images:
            self.progress_update.emit(30, f"正在获取 {location} 的所有虚拟机镜像...")
        else:
            self.progress_update.emit(50, f"正在获取 {location} 的常用虚拟机镜像...")

        vm_images = self.auth_client.get_vm_images(location, get_all_images)
        self.progress_update.emit(100, "虚拟机镜像加载完成")
        self.data_loaded.emit('vm_images', vm_images)
        
    def create_virtual_machine(self):
        """创建虚拟机"""
        vm_config = self.kwargs.get('vm_config')
        
        self.progress_update.emit(10, "正在验证配置...")
        # 这里应该验证配置
        
        self.progress_update.emit(30, "正在创建资源组...")
        # 创建资源组（如果不存在）
        
        self.progress_update.emit(50, "正在创建网络资源...")
        # 创建虚拟网络、子网、公共IP等
        
        self.progress_update.emit(70, "正在创建虚拟机...")
        # 创建虚拟机
        
        self.progress_update.emit(90, "正在配置网络安全组...")
        # 配置网络安全组规则
        
        self.progress_update.emit(100, "虚拟机创建完成")
        self.operation_complete.emit(f"虚拟机 {vm_config.get('vm_name')} 创建成功")


class VMCreationWidget(QWidget):
    """虚拟机创建组件"""
    
    def __init__(self):
        super().__init__()
        self.auth_info = None
        self.locations = []
        self.vm_sizes = []
        self.vm_images = []
        self.init_ui()
        self.setEnabled(False)  # 初始状态禁用
        
    def init_ui(self):
        """初始化UI"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(scroll_area)
        
        layout = QVBoxLayout(scroll_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 基本配置组
        basic_group = QGroupBox("基本配置")
        basic_layout = QFormLayout(basic_group)
        
        # 资源组名称
        self.rg_name_edit = QLineEdit()
        self.rg_name_edit.setPlaceholderText("输入资源组名称")
        basic_layout.addRow("资源组名称:", self.rg_name_edit)
        
        # 虚拟机名称
        self.vm_name_edit = QLineEdit()
        self.vm_name_edit.setPlaceholderText("输入虚拟机名称")
        self.vm_name_edit.textChanged.connect(self.on_vm_name_changed)
        basic_layout.addRow("虚拟机名称:", self.vm_name_edit)
        
        # 区域选择
        region_layout = QHBoxLayout()
        self.region_combo = QComboBox()
        self.region_combo.currentTextChanged.connect(self.on_region_changed)
        region_layout.addWidget(self.region_combo)
        
        self.refresh_regions_btn = QPushButton("刷新")
        self.refresh_regions_btn.clicked.connect(self.refresh_regions)
        region_layout.addWidget(self.refresh_regions_btn)
        
        basic_layout.addRow("区域:", region_layout)
        
        # 虚拟机配置选择
        size_layout = QHBoxLayout()
        self.vm_size_combo = QComboBox()
        size_layout.addWidget(self.vm_size_combo)
        
        self.refresh_sizes_btn = QPushButton("刷新")
        self.refresh_sizes_btn.clicked.connect(self.refresh_vm_sizes)
        self.refresh_sizes_btn.setEnabled(False)
        size_layout.addWidget(self.refresh_sizes_btn)
        
        basic_layout.addRow("虚拟机配置:", size_layout)
        
        # 镜像选择
        image_layout = QHBoxLayout()
        self.vm_image_combo = QComboBox()
        image_layout.addWidget(self.vm_image_combo)

        self.refresh_images_btn = QPushButton("刷新常用")
        self.refresh_images_btn.clicked.connect(self.refresh_vm_images)
        self.refresh_images_btn.setEnabled(False)
        image_layout.addWidget(self.refresh_images_btn)

        self.refresh_all_images_btn = QPushButton("刷新全部")
        self.refresh_all_images_btn.clicked.connect(self.refresh_all_vm_images)
        image_layout.addWidget(self.refresh_all_images_btn)

        basic_layout.addRow("操作系统镜像:", image_layout)
        
        layout.addWidget(basic_group)
        
        # 认证配置组
        auth_group = QGroupBox("认证配置")
        auth_layout = QFormLayout(auth_group)
        
        self.username_edit = QLineEdit()
        self.username_edit.setText("ubuntu")  # 默认用户名
        self.username_edit.setPlaceholderText("输入用户名")
        auth_layout.addRow("用户名:", self.username_edit)
        
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setPlaceholderText("输入密码")
        auth_layout.addRow("密码:", self.password_edit)
        
        layout.addWidget(auth_group)
        
        # 网络配置组
        network_group = QGroupBox("网络配置")
        network_layout = QVBoxLayout(network_group)
        
        # 端口开放选项
        port_label = QLabel("开放端口:")
        port_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        network_layout.addWidget(port_label)
        
        port_layout = QHBoxLayout()
        self.ssh_checkbox = QCheckBox("SSH (22)")
        self.http_checkbox = QCheckBox("HTTP (80)")
        self.https_checkbox = QCheckBox("HTTPS (443)")
        
        port_layout.addWidget(self.ssh_checkbox)
        port_layout.addWidget(self.http_checkbox)
        port_layout.addWidget(self.https_checkbox)
        port_layout.addStretch()
        
        network_layout.addLayout(port_layout)
        
        # 公共IP配置
        ip_layout = QFormLayout()
        
        self.create_ip_checkbox = QCheckBox("创建公共IP")
        self.create_ip_checkbox.setChecked(True)
        ip_layout.addRow(self.create_ip_checkbox)
        
        self.ip_name_edit = QLineEdit()
        self.ip_name_edit.setPlaceholderText("输入公共IP名称")
        self.ip_name_edit.textChanged.connect(self.on_ip_name_manually_changed)
        ip_layout.addRow("公共IP名称:", self.ip_name_edit)

        # 标记是否为自动生成的IP名称
        self.ip_name_auto_generated = True
        
        network_layout.addLayout(ip_layout)
        
        layout.addWidget(network_group)
        
        # 存储配置组
        storage_group = QGroupBox("存储配置")
        storage_layout = QFormLayout(storage_group)
        
        self.disk_size_spin = QSpinBox()
        self.disk_size_spin.setRange(30, 1024)
        self.disk_size_spin.setValue(64)
        self.disk_size_spin.setSuffix(" GB")
        storage_layout.addRow("磁盘大小:", self.disk_size_spin)
        
        layout.addWidget(storage_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("请配置虚拟机参数")
        layout.addWidget(self.status_label)
        
        # 创建按钮
        self.create_btn = QPushButton("创建虚拟机")
        self.create_btn.clicked.connect(self.create_virtual_machine)
        self.create_btn.setEnabled(False)
        layout.addWidget(self.create_btn)
        
        # 添加弹性空间
        layout.addStretch()

        # 连接信号以检查按钮状态
        self.setup_signal_connections()

    def setup_signal_connections(self):
        """设置信号连接"""
        # 连接文本框变化信号
        self.rg_name_edit.textChanged.connect(self.check_create_button_state)
        self.vm_name_edit.textChanged.connect(self.check_create_button_state)
        self.username_edit.textChanged.connect(self.check_create_button_state)
        self.password_edit.textChanged.connect(self.check_create_button_state)

        # 连接下拉框变化信号
        self.region_combo.currentTextChanged.connect(self.on_region_changed)
        self.vm_size_combo.currentTextChanged.connect(self.check_create_button_state)
        self.vm_image_combo.currentTextChanged.connect(self.check_create_button_state)

    def set_auth_info(self, auth_info):
        """设置认证信息"""
        self.auth_info = auth_info
        self.locations = auth_info.get('locations', [])
        self.update_region_combo()
        
    def update_region_combo(self):
        """更新区域下拉框"""
        self.region_combo.clear()
        for location in self.locations:
            self.region_combo.addItem(
                location.get('displayName', ''),
                location.get('name', '')
            )
            
    def refresh_regions(self):
        """刷新区域列表"""
        if not self.auth_info or not self.auth_info.get('auth_client'):
            return
            
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.refresh_regions_btn.setEnabled(False)
        
        self.worker = VMCreationWorker(
            self.auth_info['auth_client'],
            'load_locations'
        )
        self.worker.progress_update.connect(self.on_progress_update)
        self.worker.data_loaded.connect(self.on_data_loaded)
        self.worker.operation_failed.connect(self.on_operation_failed)
        self.worker.start()
        
    def on_region_changed(self):
        """区域变化处理"""
        if self.region_combo.currentData():
            self.refresh_sizes_btn.setEnabled(True)
            self.refresh_images_btn.setEnabled(True)
            self.check_create_button_state()
            
    def refresh_vm_sizes(self):
        """刷新虚拟机规格"""
        location = self.region_combo.currentData()
        if not location:
            return
            
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.refresh_sizes_btn.setEnabled(False)
        
        self.worker = VMCreationWorker(
            self.auth_info['auth_client'],
            'load_vm_sizes',
            location=location
        )
        self.worker.progress_update.connect(self.on_progress_update)
        self.worker.data_loaded.connect(self.on_data_loaded)
        self.worker.operation_failed.connect(self.on_operation_failed)
        self.worker.start()
        
    def refresh_vm_images(self):
        """刷新虚拟机镜像"""
        location = self.region_combo.currentData()
        if not location:
            return
            
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.refresh_images_btn.setEnabled(False)
        
        self.worker = VMCreationWorker(
            self.auth_info['auth_client'],
            'load_vm_images',
            location=location
        )
        self.worker.progress_update.connect(self.on_progress_update)
        self.worker.data_loaded.connect(self.on_data_loaded)
        self.worker.operation_failed.connect(self.on_operation_failed)
        self.worker.start()
        
    def on_progress_update(self, value, message):
        """进度更新"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        
    def on_data_loaded(self, data_type, data):
        """数据加载完成"""
        self.progress_bar.setVisible(False)
        
        if data_type == 'locations':
            self.locations = data
            self.update_region_combo()
            self.refresh_regions_btn.setEnabled(True)
            self.status_label.setText("区域列表已更新")
            
        elif data_type == 'vm_sizes':
            self.vm_sizes = data
            self.update_vm_size_combo()
            self.refresh_sizes_btn.setEnabled(True)
            self.status_label.setText("虚拟机规格已更新")
            
        elif data_type == 'vm_images':
            self.vm_images = data
            self.update_vm_image_combo()
            self.refresh_images_btn.setEnabled(True)
            self.refresh_all_images_btn.setEnabled(True)
            self.status_label.setText(f"虚拟机镜像已更新，共 {len(data)} 个镜像")
            
        self.check_create_button_state()
        
    def update_vm_size_combo(self):
        """更新虚拟机规格下拉框"""
        self.vm_size_combo.clear()
        for size in self.vm_sizes:
            display_text = f"{size.get('name')} ({size.get('numberOfCores')}核, {size.get('memoryInMB')}MB内存)"
            self.vm_size_combo.addItem(display_text, size.get('name'))
            
    def update_vm_image_combo(self):
        """更新虚拟机镜像下拉框"""
        self.vm_image_combo.clear()
        for image in self.vm_images:
            self.vm_image_combo.addItem(image.get('displayName'), image)
            
    def check_create_button_state(self):
        """检查创建按钮状态"""
        can_create = (
            bool(self.rg_name_edit.text().strip()) and
            bool(self.vm_name_edit.text().strip()) and
            bool(self.username_edit.text().strip()) and
            bool(self.password_edit.text().strip()) and
            bool(self.region_combo.currentData()) and
            bool(self.vm_size_combo.currentData()) and
            bool(self.vm_image_combo.currentData())
        )

        self.create_btn.setEnabled(can_create)

    def on_vm_name_changed(self):
        """虚拟机名称变化处理"""
        vm_name = self.vm_name_edit.text().strip()

        if vm_name:
            # 自动生成资源组名称
            rg_name = f"{vm_name}-rg"
            self.rg_name_edit.setText(rg_name)

            # 如果IP名称是自动生成的，则更新IP名称
            if self.ip_name_auto_generated:
                ip_name = f"{vm_name}-ip"
                # 临时断开信号连接，避免触发手动编辑标记
                self.ip_name_edit.textChanged.disconnect()
                self.ip_name_edit.setText(ip_name)
                self.ip_name_edit.textChanged.connect(self.on_ip_name_manually_changed)
        else:
            # 如果虚拟机名称为空，清空相关字段
            self.rg_name_edit.clear()
            if self.ip_name_auto_generated:
                self.ip_name_edit.textChanged.disconnect()
                self.ip_name_edit.clear()
                self.ip_name_edit.textChanged.connect(self.on_ip_name_manually_changed)

    def on_ip_name_manually_changed(self):
        """IP名称手动编辑处理"""
        # 如果用户手动编辑了IP名称，则标记为非自动生成
        vm_name = self.vm_name_edit.text().strip()
        expected_ip_name = f"{vm_name}-ip" if vm_name else ""
        current_ip_name = self.ip_name_edit.text().strip()

        # 如果当前IP名称不是期望的自动生成名称，则标记为手动编辑
        if current_ip_name != expected_ip_name:
            self.ip_name_auto_generated = False
        else:
            self.ip_name_auto_generated = True

    def refresh_all_vm_images(self):
        """刷新所有虚拟机镜像"""
        location = self.region_combo.currentData()
        if not location:
            QMessageBox.warning(self, "警告", "请先选择区域")
            return

        if not self.auth_info:
            QMessageBox.warning(self, "警告", "请先完成Azure认证")
            return

        # 警告用户这可能需要较长时间
        reply = QMessageBox.question(
            self, '确认操作',
            '获取所有Azure镜像可能需要较长时间（几分钟），确定要继续吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.refresh_images_btn.setEnabled(False)
        self.refresh_all_images_btn.setEnabled(False)

        self.worker = VMCreationWorker(
            self.auth_info['auth_client'],
            'load_vm_images',
            location=location,
            get_all_images=True
        )
        self.worker.progress_update.connect(self.on_progress_update)
        self.worker.data_loaded.connect(self.on_data_loaded)
        self.worker.operation_failed.connect(self.on_operation_failed)
        self.worker.start()
        
    def create_virtual_machine(self):
        """创建虚拟机"""
        # 收集配置信息
        vm_config = {
            'rg_name': self.rg_name_edit.text().strip(),
            'vm_name': self.vm_name_edit.text().strip(),
            'location': self.region_combo.currentData(),
            'vm_size': self.vm_size_combo.currentData(),
            'vm_image': self.vm_image_combo.currentData(),
            'username': self.username_edit.text().strip(),
            'password': self.password_edit.text().strip(),
            'open_ssh': self.ssh_checkbox.isChecked(),
            'open_http': self.http_checkbox.isChecked(),
            'open_https': self.https_checkbox.isChecked(),
            'create_public_ip': self.create_ip_checkbox.isChecked(),
            'public_ip_name': self.ip_name_edit.text().strip(),
            'disk_size': self.disk_size_spin.value()
        }
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.create_btn.setEnabled(False)
        
        self.worker = VMCreationWorker(
            self.auth_info['auth_client'],
            'create_vm',
            vm_config=vm_config
        )
        self.worker.progress_update.connect(self.on_progress_update)
        self.worker.operation_complete.connect(self.on_create_complete)
        self.worker.operation_failed.connect(self.on_operation_failed)
        self.worker.start()
        
    def on_create_complete(self, message):
        """创建完成"""
        self.progress_bar.setVisible(False)
        self.create_btn.setEnabled(True)
        self.status_label.setText("虚拟机创建完成")
        
        QMessageBox.information(self, "创建成功", message)
        
    def on_operation_failed(self, error_msg):
        """操作失败"""
        self.progress_bar.setVisible(False)
        self.refresh_regions_btn.setEnabled(True)
        self.refresh_sizes_btn.setEnabled(True)
        self.refresh_images_btn.setEnabled(True)
        self.refresh_all_images_btn.setEnabled(True)
        self.create_btn.setEnabled(True)
        self.status_label.setText("操作失败")

        QMessageBox.critical(self, "错误", error_msg)
