#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源组管理对话框
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QLabel, QMessageBox, QTextEdit, QProgressBar,
                             QInputDialog)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont


class ResourceGroupDeleteWorker(QThread):
    """资源组删除工作线程"""
    progress_update = pyqtSignal(int, str)
    delete_complete = pyqtSignal(dict)
    delete_failed = pyqtSignal(str)

    def __init__(self, auth_client, resource_group_name):
        super().__init__()
        self.auth_client = auth_client
        self.resource_group_name = resource_group_name

    def run(self):
        """执行删除操作"""
        try:
            def progress_callback(value, message):
                self.progress_update.emit(value, message)

            result = self.auth_client.delete_resource_group(
                self.resource_group_name,
                progress_callback
            )

            if result['success']:
                self.delete_complete.emit(result)
            else:
                self.delete_failed.emit(result['error'])

        except Exception as e:
            self.delete_failed.emit(f"删除过程中发生错误: {str(e)}")


class ResourceGroupDialog(QDialog):
    """资源组管理对话框"""
    
    def __init__(self, vm_widget, parent=None):
        super().__init__(parent)
        self.vm_widget = vm_widget
        self.delete_worker = None  # 删除工作线程
        self.init_ui()
        # 延迟加载数据，避免阻塞UI
        from PyQt6.QtCore import QTimer
        QTimer.singleShot(100, self.load_data)
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("资源组管理")
        self.setModal(True)
        self.resize(900, 500)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("资源组列表")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_data)
        button_layout.addWidget(self.refresh_btn)

        self.view_details_btn = QPushButton("查看详情")
        self.view_details_btn.setEnabled(False)
        self.view_details_btn.clicked.connect(self.view_resource_group_details)
        button_layout.addWidget(self.view_details_btn)

        self.delete_rg_btn = QPushButton("删除资源组")
        self.delete_rg_btn.setEnabled(False)
        self.delete_rg_btn.clicked.connect(self.delete_resource_group)
        button_layout.addWidget(self.delete_rg_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 资源组表格
        self.rg_table = QTableWidget()
        self.rg_table.setColumnCount(3)
        self.rg_table.setHorizontalHeaderLabels([
            "名称", "位置", "标签"
        ])
        
        # 设置表格属性
        header = self.rg_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.rg_table.setAlternatingRowColors(True)
        self.rg_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.rg_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(self.rg_table)
        
        # 详情显示区域
        details_label = QLabel("资源组详情:")
        details_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(details_label)
        
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(150)
        self.details_text.setReadOnly(True)
        self.details_text.setPlaceholderText("选择资源组查看详细信息...")
        layout.addWidget(self.details_text)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)

        # 关闭按钮
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        close_layout.addWidget(close_btn)
        
        layout.addLayout(close_layout)
        
    def load_data(self):
        """加载数据"""
        if hasattr(self.vm_widget, 'resource_groups_data'):
            self.update_rg_table(self.vm_widget.resource_groups_data)
        else:
            self.refresh_data()
            
    def refresh_data(self):
        """刷新数据"""
        self.vm_widget.refresh_data()
        # 连接信号以更新表格
        if hasattr(self.vm_widget, 'worker'):
            self.vm_widget.worker.data_loaded.connect(self.on_data_loaded)
        
    def on_data_loaded(self, data):
        """数据加载完成"""
        if data and len(data) > 0:
            result = data[0]
            resource_groups = result.get('resource_groups', [])
            self.update_rg_table(resource_groups)
            
    def update_rg_table(self, rg_data):
        """更新资源组表格"""
        self.rg_table.setRowCount(len(rg_data))
        
        for row, rg in enumerate(rg_data):
            self.rg_table.setItem(row, 0, QTableWidgetItem(rg.get('name', '')))
            self.rg_table.setItem(row, 1, QTableWidgetItem(rg.get('location', '')))
            
            # 处理标签
            tags = rg.get('tags', {})
            tag_str = ', '.join([f"{k}:{v}" for k, v in tags.items()]) if tags else "无"
            self.rg_table.setItem(row, 2, QTableWidgetItem(tag_str))
            
    def on_selection_changed(self):
        """选择变化"""
        selected_rows = self.rg_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0
        
        self.view_details_btn.setEnabled(has_selection)
        self.delete_rg_btn.setEnabled(has_selection)
        
        if has_selection:
            self.view_resource_group_details()
        else:
            self.details_text.clear()
            
    def view_resource_group_details(self):
        """查看资源组详情"""
        selected_rows = self.rg_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        rg_name = self.rg_table.item(row, 0).text()

        # 显示加载状态
        self.details_text.setPlainText("正在加载资源组详细信息...")

        # 获取详细信息
        try:
            if hasattr(self.vm_widget, 'auth_info') and self.vm_widget.auth_info:
                auth_client = self.vm_widget.auth_info.get('auth_client')
                if auth_client:
                    details_data = auth_client.get_resource_group_details(rg_name)
                    self.display_resource_group_details(details_data)
                else:
                    self.details_text.setPlainText("错误：认证客户端不可用")
            else:
                self.details_text.setPlainText("错误：未找到认证信息")
        except Exception as e:
            self.details_text.setPlainText(f"获取详细信息失败: {str(e)}")

    def display_resource_group_details(self, details_data):
        """显示资源组详细信息"""
        if not details_data:
            self.details_text.setPlainText("无法获取资源组详细信息")
            return

        # 构建详细信息文本
        details_text = f"""资源组详细信息
{'='*50}

基本信息:
  名称: {details_data.get('name', 'Unknown')}
  位置: {details_data.get('location', 'Unknown')}
  资源ID: {details_data.get('id', 'Unknown')}

标签:"""

        tags = details_data.get('tags', {})
        if tags:
            for key, value in tags.items():
                details_text += f"\n  {key}: {value}"
        else:
            details_text += "\n  无标签"

        # 显示虚拟机信息
        vms = details_data.get('virtual_machines', [])
        details_text += f"\n\n虚拟机 ({len(vms)} 台):"
        if vms:
            for vm in vms:
                details_text += f"\n  名称: {vm['name']}"
                details_text += f"\n    规格: {vm['size']}"
                details_text += f"\n    位置: {vm['location']}"

                if vm['private_ips']:
                    details_text += f"\n    私有IP: {', '.join(vm['private_ips'])}"
                else:
                    details_text += f"\n    私有IP: 无"

                if vm['public_ips']:
                    details_text += f"\n    公共IP: {', '.join(vm['public_ips'])}"
                else:
                    details_text += f"\n    公共IP: 无"
                details_text += "\n"
        else:
            details_text += "\n  无虚拟机"

        # 显示其他资源
        resources = details_data.get('resources', [])
        other_resources = [r for r in resources if 'Microsoft.Compute/virtualMachines' not in r['type']]
        details_text += f"\n其他资源 ({len(other_resources)} 个):"
        if other_resources:
            for resource in other_resources[:10]:  # 只显示前10个
                resource_type = resource['type'].split('/')[-1] if '/' in resource['type'] else resource['type']
                details_text += f"\n  {resource['name']} ({resource_type})"
            if len(other_resources) > 10:
                details_text += f"\n  ... 还有 {len(other_resources) - 10} 个资源"
        else:
            details_text += "\n  无其他资源"

        self.details_text.setPlainText(details_text)
        
    def delete_resource_group(self):
        """删除资源组"""
        selected_rows = self.rg_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        rg_name = self.rg_table.item(row, 0).text()

        # 确认删除
        reply = QMessageBox.question(
            self, '确认删除',
            f'确定要删除资源组 "{rg_name}" 吗？\n\n'
            f'警告：这将永久删除资源组中的所有资源！\n'
            f'包括虚拟机、网络、存储等所有内容。\n\n'
            f'此操作不可撤销，请谨慎操作！',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 再次确认
            confirm_reply = QMessageBox.question(
                self, '最终确认',
                f'您即将删除资源组 "{rg_name}" 及其所有资源。\n\n'
                f'请输入资源组名称以确认删除：\n'
                f'（在下一个对话框中输入 "{rg_name}"）',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if confirm_reply == QMessageBox.StandardButton.Yes:
                from PyQt6.QtWidgets import QInputDialog
                text, ok = QInputDialog.getText(
                    self, '确认删除',
                    f'请输入资源组名称 "{rg_name}" 以确认删除：'
                )

                if ok and text.strip() == rg_name:
                    self.start_delete_operation(rg_name)
                elif ok:
                    QMessageBox.warning(self, "错误", "输入的资源组名称不匹配，删除操作已取消")

    def start_delete_operation(self, rg_name):
        """开始删除操作"""
        # 检查认证信息
        if not hasattr(self.vm_widget, 'auth_info') or not self.vm_widget.auth_info:
            QMessageBox.warning(self, "错误", "未找到认证信息")
            return

        auth_client = self.vm_widget.auth_info.get('auth_client')
        if not auth_client:
            QMessageBox.warning(self, "错误", "认证客户端不可用")
            return

        # 停止之前的删除操作（如果有）
        if self.delete_worker and self.delete_worker.isRunning():
            self.delete_worker.terminate()
            self.delete_worker.wait()

        # 显示进度条和状态
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在准备删除操作...")

        # 禁用按钮
        self.refresh_btn.setEnabled(False)
        self.view_details_btn.setEnabled(False)
        self.delete_rg_btn.setEnabled(False)

        # 启动删除工作线程
        self.delete_worker = ResourceGroupDeleteWorker(auth_client, rg_name)
        self.delete_worker.progress_update.connect(self.on_delete_progress)
        self.delete_worker.delete_complete.connect(self.on_delete_complete)
        self.delete_worker.delete_failed.connect(self.on_delete_failed)
        self.delete_worker.finished.connect(self.on_delete_worker_finished)
        self.delete_worker.start()

    def on_delete_progress(self, value, message):
        """删除进度更新"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)

    def on_delete_complete(self, result):
        """删除完成"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("删除操作完成")

        # 重新启用按钮
        self.refresh_btn.setEnabled(True)
        self.view_details_btn.setEnabled(True)
        self.delete_rg_btn.setEnabled(True)

        # 显示成功消息
        QMessageBox.information(self, "删除成功", result['message'])

        # 刷新数据
        self.refresh_data()

    def on_delete_failed(self, error_msg):
        """删除失败"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("删除操作失败")

        # 重新启用按钮
        self.refresh_btn.setEnabled(True)
        self.view_details_btn.setEnabled(True)
        self.delete_rg_btn.setEnabled(True)

        # 显示错误消息
        QMessageBox.critical(self, "删除失败", f"删除资源组失败：\n\n{error_msg}")

    def on_delete_worker_finished(self):
        """删除工作线程完成"""
        if self.delete_worker:
            self.delete_worker.deleteLater()
            self.delete_worker = None

    def closeEvent(self, event):
        """对话框关闭事件"""
        # 如果有删除操作正在进行，询问用户是否确认关闭
        if self.delete_worker and self.delete_worker.isRunning():
            reply = QMessageBox.question(
                self, '确认关闭',
                '资源组删除操作正在进行中，确定要关闭对话框吗？\n\n'
                '注意：关闭对话框不会停止删除操作，删除将在后台继续进行。',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                event.ignore()
                return

        # 清理工作线程
        if self.delete_worker and self.delete_worker.isRunning():
            self.delete_worker.terminate()
            self.delete_worker.wait(3000)  # 等待最多3秒

        event.accept()
