#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI工具函数
"""

from PyQt6.QtCore import QTimer
from PyQt6.QtWidgets import QPushButton
import functools


def immediate_response(loading_text="处理中..."):
    """
    装饰器：确保按钮点击立即响应
    
    Args:
        loading_text (str): 加载时显示的文本
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            # 如果是按钮点击事件，立即更新按钮状态
            sender = getattr(self, 'sender', None)
            if sender and callable(sender):
                button = sender()
                if isinstance(button, QPushButton):
                    original_text = button.text()
                    button.setEnabled(False)
                    button.setText(loading_text)
                    
                    def restore_button():
                        button.setEnabled(True)
                        button.setText(original_text)
                    
                    # 延迟执行实际操作
                    def delayed_execution():
                        try:
                            result = func(self, *args, **kwargs)
                            return result
                        except Exception as e:
                            print(f"操作执行失败: {str(e)}")
                        finally:
                            restore_button()
                    
                    QTimer.singleShot(50, delayed_execution)
                    return
            
            # 如果不是按钮点击，直接执行
            return func(self, *args, **kwargs)
        return wrapper
    return decorator


def make_button_responsive(button, target_method, loading_text="处理中..."):
    """
    使按钮响应更快
    
    Args:
        button (QPushButton): 按钮对象
        target_method (callable): 目标方法
        loading_text (str): 加载时显示的文本
    """
    original_text = button.text()
    
    def responsive_handler():
        # 立即更新按钮状态
        button.setEnabled(False)
        button.setText(loading_text)
        
        def restore_button():
            button.setEnabled(True)
            button.setText(original_text)
        
        def delayed_execution():
            try:
                target_method()
            except Exception as e:
                print(f"操作执行失败: {str(e)}")
            finally:
                restore_button()
        
        # 延迟执行实际操作
        QTimer.singleShot(50, delayed_execution)
    
    return responsive_handler


def setup_responsive_buttons(widget):
    """
    为组件中的所有按钮设置响应式处理
    
    Args:
        widget: 包含按钮的组件
    """
    # 查找所有QPushButton
    buttons = widget.findChildren(QPushButton)
    
    for button in buttons:
        # 获取按钮的原始点击处理器
        if hasattr(button, 'clicked'):
            # 保存原始处理器
            original_handlers = []
            
            # 断开所有现有连接
            try:
                button.clicked.disconnect()
            except:
                pass
            
            # 重新连接响应式处理器
            responsive_handler = make_button_responsive(
                button, 
                lambda: None,  # 占位符，实际处理器会在运行时确定
                f"正在{button.text()}..."
            )
            button.clicked.connect(responsive_handler)


class ResponsiveWidget:
    """响应式组件混入类"""
    
    def make_all_buttons_responsive(self):
        """使所有按钮都响应式"""
        setup_responsive_buttons(self)
        
    def connect_responsive_button(self, button, handler, loading_text=None):
        """连接响应式按钮"""
        if loading_text is None:
            loading_text = f"正在{button.text()}..."
            
        responsive_handler = make_button_responsive(button, handler, loading_text)
        button.clicked.connect(responsive_handler)


def show_loading_cursor():
    """显示加载光标"""
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import Qt
    QApplication.setOverrideCursor(Qt.CursorShape.WaitCursor)


def hide_loading_cursor():
    """隐藏加载光标"""
    from PyQt6.QtWidgets import QApplication
    QApplication.restoreOverrideCursor()


def with_loading_cursor(func):
    """装饰器：在操作期间显示加载光标"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        show_loading_cursor()
        try:
            return func(*args, **kwargs)
        finally:
            hide_loading_cursor()
    return wrapper


def delayed_execution(delay_ms=100):
    """
    装饰器：延迟执行方法，避免阻塞UI
    
    Args:
        delay_ms (int): 延迟毫秒数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            def delayed_func():
                return func(self, *args, **kwargs)
            
            QTimer.singleShot(delay_ms, delayed_func)
        return wrapper
    return decorator
