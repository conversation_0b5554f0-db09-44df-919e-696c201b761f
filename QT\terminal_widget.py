#!/usr/bin/env python3
"""
Ubuntu Terminal Widget Implementation
A QTextEdit-based terminal widget that mimics Ubuntu terminal behavior
"""

import os
import sys
import subprocess
import threading
import re
from PyQt5.QtWidgets import QTextEdit, QApplication, QMenu
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QProcess
from PyQt5.QtGui import QFont, QTextCursor, QColor, QKeyEvent

from command_executor import CommandExecutor


class UbuntuTerminal(QTextEdit):
    """Ubuntu-style terminal widget with real command execution"""
    
    # Signals
    command_executed = pyqtSignal(str, str)  # command, output
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_terminal()
        self.setup_executor()
        
    def init_terminal(self):
        """Initialize terminal appearance and behavior"""
        # Set Ubuntu terminal styling
        self.setStyleSheet("""
            QTextEdit {
                background-color: #000000;
                color: #FFFFFF;
                border: none;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 15px;
                selection-background-color: #4A90E2;
            }
        """)
        
        # Set font
        font = QFont("Consolas", 15)
        if not font.exactMatch():
            font = QFont("Courier New", 15)
        font.setFixedPitch(True)
        self.setFont(font)
        
        # Terminal state
        self.current_directory = os.path.expanduser("~")
        self.command_history = []
        self.history_index = -1
        self.current_line_start = 0

        # Device code detection
        self.recent_output = ""  # Store recent output for device code detection
        
        # Initialize terminal
        self.show_prompt()
        
    def setup_executor(self):
        """Setup command executor"""
        self.executor = CommandExecutor()
        self.executor.output_ready.connect(self.handle_command_output)
        self.executor.error_ready.connect(self.handle_command_error)
        self.executor.finished.connect(self.handle_command_finished)
        
    def show_prompt(self):
        """Display the Ubuntu-style prompt"""
        username = os.getenv('USER', 'user')
        hostname = os.getenv('HOSTNAME', 'ubuntu')
        
        # Get current directory for prompt
        home = os.path.expanduser("~")
        if self.current_directory.startswith(home):
            display_dir = "~" + self.current_directory[len(home):]
        else:
            display_dir = self.current_directory
            
        prompt = f"{username}@{hostname}:{display_dir}$ "
        
        # Add prompt with green color
        self.append_colored_text(prompt, "#4E9A06")
        self.current_line_start = self.textCursor().position()
        
    def append_colored_text(self, text, color="#FFFFFF"):
        """Append text with specified color"""
        cursor = self.textCursor()
        cursor.movePosition(QTextCursor.End)
        
        # Set text color
        format = cursor.charFormat()
        format.setForeground(QColor(color))
        cursor.setCharFormat(format)
        
        cursor.insertText(text)
        self.setTextCursor(cursor)
        
    def keyPressEvent(self, event: QKeyEvent):
        """Handle key press events for terminal interaction"""
        cursor = self.textCursor()

        # Allow free movement when selecting text (Shift key held)
        # Only prevent editing before current line start when not selecting
        if not (event.modifiers() & Qt.ShiftModifier) and cursor.position() < self.current_line_start:
            cursor.setPosition(self.current_line_start)
            self.setTextCursor(cursor)

        # Handle Ctrl key combinations
        if event.modifiers() & Qt.ControlModifier:
            if event.key() == Qt.Key_C:
                # Check if there's selected text
                if self.textCursor().hasSelection():
                    # If text is selected, perform copy operation
                    self.copy()
                else:
                    # If no text is selected, handle as interrupt
                    self.handle_ctrl_c()
                return
            elif event.key() == Qt.Key_L:
                self.clear()
                self.show_prompt()
                return
            elif event.key() == Qt.Key_A:
                # Ctrl+A: Select all text (standard behavior)
                self.selectAll()
                return
            elif event.key() == Qt.Key_Home:
                # Ctrl+Home: Move to beginning of current line (terminal behavior)
                cursor.setPosition(self.current_line_start)
                self.setTextCursor(cursor)
                return
            elif event.key() == Qt.Key_V:
                # Paste text from clipboard
                self.handle_paste()
                return
            elif event.key() == Qt.Key_E:
                cursor.movePosition(QTextCursor.End)
                self.setTextCursor(cursor)
                return
            elif event.key() == Qt.Key_U:
                # Clear line from cursor to beginning
                cursor.setPosition(self.current_line_start)
                cursor.movePosition(QTextCursor.End, QTextCursor.KeepAnchor)
                cursor.removeSelectedText()
                return
            elif event.key() == Qt.Key_K:
                # Clear line from cursor to end
                cursor.movePosition(QTextCursor.End, QTextCursor.KeepAnchor)
                cursor.removeSelectedText()
                return

        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.execute_command()
        elif event.key() == Qt.Key_Up:
            self.navigate_history(-1)
        elif event.key() == Qt.Key_Down:
            self.navigate_history(1)
        elif event.key() == Qt.Key_Backspace:
            # Only allow backspace if cursor is after current line start and not selecting
            if cursor.position() > self.current_line_start and not cursor.hasSelection():
                super().keyPressEvent(event)
            elif cursor.hasSelection():
                # If text is selected, allow normal backspace behavior
                super().keyPressEvent(event)
        elif event.key() == Qt.Key_Delete:
            # Only allow delete if cursor is at or after current line start and not selecting
            if cursor.position() >= self.current_line_start and not cursor.hasSelection():
                super().keyPressEvent(event)
            elif cursor.hasSelection():
                # If text is selected, allow normal delete behavior
                super().keyPressEvent(event)
        elif event.key() == Qt.Key_Left:
            # Allow free movement when selecting text (Shift key held)
            if (event.modifiers() & Qt.ShiftModifier) or cursor.position() > self.current_line_start:
                super().keyPressEvent(event)
        elif event.key() == Qt.Key_Home:
            cursor.setPosition(self.current_line_start)
            self.setTextCursor(cursor)
        elif event.key() == Qt.Key_End:
            cursor.movePosition(QTextCursor.End)
            self.setTextCursor(cursor)
        elif event.key() == Qt.Key_Tab:
            self.handle_tab_completion()
        else:
            # For normal text input, ensure cursor is at or after current line start
            if cursor.position() < self.current_line_start and not cursor.hasSelection():
                cursor.setPosition(self.current_line_start)
                self.setTextCursor(cursor)
            # Allow normal text input
            super().keyPressEvent(event)
            
    def get_current_command(self):
        """Get the current command being typed"""
        cursor = self.textCursor()
        cursor.setPosition(self.current_line_start)
        cursor.movePosition(QTextCursor.End, QTextCursor.KeepAnchor)
        return cursor.selectedText().strip()
        
    def execute_command(self):
        """Execute the current command"""
        command = self.get_current_command()

        if command.strip():
            # Reset device code detection for new command
            self.recent_output = ""
            if hasattr(self, '_last_copied_code'):
                delattr(self, '_last_copied_code')

            # Add to history
            self.command_history.append(command)
            self.history_index = len(self.command_history)

            # Move to new line
            self.append("\n")

            # Handle built-in commands
            if self.handle_builtin_command(command):
                self.show_prompt()
                return

            # Execute external command
            self.executor.execute_command(command, self.current_directory)
        else:
            self.append("\n")
            self.show_prompt()
            
    def handle_builtin_command(self, command):
        """Handle built-in terminal commands"""
        parts = command.strip().split()
        if not parts:
            return True
            
        cmd = parts[0]
        
        if cmd == "cd":
            return self.handle_cd_command(parts[1:])
        elif cmd == "clear":
            self.clear()
            return True
        elif cmd == "pwd":
            self.append_colored_text(self.current_directory + "\n", "#FFFFFF")
            return True
        elif cmd == "exit":
            QApplication.quit()
            return True
            
        return False
        
    def handle_cd_command(self, args):
        """Handle cd command"""
        if not args:
            target = os.path.expanduser("~")
        else:
            target = args[0]
            
        # Handle relative paths
        if not os.path.isabs(target):
            target = os.path.join(self.current_directory, target)
            
        # Normalize path
        target = os.path.normpath(target)
        
        if os.path.isdir(target):
            self.current_directory = target
        else:
            self.append_colored_text(f"cd: {target}: No such file or directory\n", "#CC0000")
            
        return True
        
    def navigate_history(self, direction):
        """Navigate command history"""
        if not self.command_history:
            return
            
        self.history_index += direction
        self.history_index = max(0, min(self.history_index, len(self.command_history)))
        
        # Clear current command
        cursor = self.textCursor()
        cursor.setPosition(self.current_line_start)
        cursor.movePosition(QTextCursor.End, QTextCursor.KeepAnchor)
        cursor.removeSelectedText()
        
        # Insert history command
        if self.history_index < len(self.command_history):
            cursor.insertText(self.command_history[self.history_index])
            
    def handle_tab_completion(self):
        """Handle tab completion for files and commands"""
        command = self.get_current_command()
        parts = command.split()
        
        if not parts:
            return
            
        # Simple file completion for the last argument
        last_part = parts[-1] if parts else ""
        
        try:
            if "/" in last_part:
                directory = os.path.dirname(last_part)
                prefix = os.path.basename(last_part)
                search_dir = os.path.join(self.current_directory, directory) if not os.path.isabs(directory) else directory
            else:
                directory = ""
                prefix = last_part
                search_dir = self.current_directory
                
            if os.path.isdir(search_dir):
                matches = []
                for item in os.listdir(search_dir):
                    if item.startswith(prefix):
                        matches.append(item)
                        
                if len(matches) == 1:
                    # Complete the match
                    cursor = self.textCursor()
                    cursor.setPosition(self.current_line_start)
                    cursor.movePosition(QTextCursor.End, QTextCursor.KeepAnchor)
                    
                    completed_command = " ".join(parts[:-1] + [os.path.join(directory, matches[0])])
                    cursor.removeSelectedText()
                    cursor.insertText(completed_command)
                    
        except (OSError, PermissionError):
            pass
            
    def handle_command_output(self, output):
        """Handle command output from executor"""
        self.append_colored_text(output, "#FFFFFF")

        # Accumulate recent output for device code detection
        self.recent_output += output
        # Keep only last 1000 characters to avoid memory issues
        if len(self.recent_output) > 1000:
            self.recent_output = self.recent_output[-1000:]

        # Check for Azure device code and copy to clipboard
        self.check_and_copy_device_code(self.recent_output)
        
    def handle_command_error(self, error):
        """Handle command error from executor"""
        self.append_colored_text(error, "#CC0000")
        
    def handle_command_finished(self, exit_code):
        """Handle command completion"""
        # Reset device code detection for next command
        if hasattr(self, '_last_copied_code'):
            delattr(self, '_last_copied_code')
        self.recent_output = ""
        self.show_prompt()

    def check_and_copy_device_code(self, output):
        """Check for Azure device code in output and copy to clipboard"""
        # Skip if we already found and copied a code recently
        if hasattr(self, '_last_copied_code'):
            return

        # Debug: print output when it contains "code"
        if "code" in output.lower():
            print(f"[DEBUG] Output contains 'code': {repr(output[-200:])}")  # Last 200 chars

        # Enhanced patterns to match Azure device code
        # Look for 8-9 character alphanumeric codes in various contexts
        device_code_patterns = [
            r'code\s+([A-Z0-9]{8,9})',  # "code A1B2C3D4"
            r'enter\s+the\s+code\s+([A-Z0-9]{8,9})',  # "enter the code N6LUMEE7Z"
            r'([A-Z0-9]{8,9})\s+to\s+authenticate',  # "A1B2C3D4 to authenticate"
            r'device\s+code[:\s]+([A-Z0-9]{8,9})',  # "device code: A1B2C3D4"
            r'authentication\s+code[:\s]+([A-Z0-9]{8,9})',  # "authentication code: A1B2C3D4"
            r'use\s+code[:\s]+([A-Z0-9]{8,9})',  # "use code: A1B2C3D4"
        ]

        for pattern in device_code_patterns:
            match = re.search(pattern, output, re.IGNORECASE | re.DOTALL)
            if match:
                device_code = match.group(1)
                print(f"[DEBUG] Found device code: {device_code}")

                # Mark that we found a code to avoid duplicates
                self._last_copied_code = device_code

                success = self.copy_to_clipboard(device_code)
                if success:
                    self.append_colored_text(f"\n[自动复制] 设备代码 {device_code} 已复制到剪贴板\n", "#00FF00")
                else:
                    self.append_colored_text(f"\n[错误] 无法复制设备代码到剪贴板\n", "#FF0000")
                break

    def copy_to_clipboard(self, text):
        """Copy text to Windows clipboard using multiple methods"""
        print(f"[DEBUG] Attempting to copy to clipboard: {text}")

        # Method 1: Windows clip command (most reliable on Windows)
        try:
            import subprocess
            process = subprocess.Popen(['clip'], stdin=subprocess.PIPE, text=True, shell=True)
            _, _ = process.communicate(input=text)
            if process.returncode == 0:
                print(f"[DEBUG] Windows clip command successful")
                return True
            else:
                print(f"[DEBUG] Windows clip command failed with return code: {process.returncode}")
        except Exception as e:
            print(f"[DEBUG] Windows clip command failed: {e}")

        # Method 2: PyQt5 clipboard
        try:
            clipboard = QApplication.clipboard()
            clipboard.setText(text)
            # Verify the copy worked
            if clipboard.text() == text:
                print(f"[DEBUG] PyQt5 clipboard copy successful")
                return True
            else:
                print(f"[DEBUG] PyQt5 clipboard verification failed")
        except Exception as e:
            print(f"[DEBUG] PyQt5 clipboard failed: {e}")

        print(f"[DEBUG] All clipboard methods failed")
        return False

    def append(self, text):
        """Override append to maintain cursor position"""
        cursor = self.textCursor()
        cursor.movePosition(QTextCursor.End)
        cursor.insertText(text)
        self.setTextCursor(cursor)

        # Auto-scroll to bottom
        scrollbar = self.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def insert_text_at_cursor(self, text):
        """Insert text at current cursor position"""
        cursor = self.textCursor()
        cursor.insertText(text)
        self.setTextCursor(cursor)

    def clear_current_line(self):
        """Clear the current command line"""
        cursor = self.textCursor()
        cursor.setPosition(self.current_line_start)
        cursor.movePosition(QTextCursor.End, QTextCursor.KeepAnchor)
        cursor.removeSelectedText()

    def get_terminal_size(self):
        """Get terminal size in characters"""
        font_metrics = self.fontMetrics()
        char_width = font_metrics.averageCharWidth()
        char_height = font_metrics.height()

        width = self.width() // char_width
        height = self.height() // char_height

        return width, height

    def handle_ctrl_c(self):
        """Handle Ctrl+C interrupt"""
        if self.executor.is_running:
            self.executor.terminate_current_command()
            self.append_colored_text("^C\n", "#CC0000")
            self.show_prompt()
        else:
            # Clear current line
            self.clear_current_line()
            self.append("\n")
            self.show_prompt()

    def handle_paste(self):
        """Handle paste operation"""
        clipboard = QApplication.clipboard()
        text = clipboard.text()

        if text:
            # Ensure cursor is at or after current line start
            cursor = self.textCursor()
            if cursor.position() < self.current_line_start:
                cursor.setPosition(self.current_line_start)
                self.setTextCursor(cursor)

            # Insert the pasted text
            cursor.insertText(text)

    def mousePressEvent(self, event):
        """Handle mouse press events"""
        # Handle right mouse button
        if event.button() == Qt.RightButton:
            self.show_context_menu(event.pos())
            return

        # Allow normal mouse selection behavior
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """Handle mouse move events for text selection"""
        # Allow normal text selection behavior
        super().mouseMoveEvent(event)

    def show_context_menu(self, position):
        """Show context menu on right click"""
        menu = QMenu(self)

        # Check if there's selected text
        has_selection = self.textCursor().hasSelection()

        if has_selection:
            # Add copy action
            copy_action = menu.addAction("复制")
            copy_action.triggered.connect(self.copy_selected_text)

        # Add paste action
        clipboard = QApplication.clipboard()
        if clipboard.text():
            paste_action = menu.addAction("粘贴")
            paste_action.triggered.connect(self.handle_paste)

        # Add select all action
        select_all_action = menu.addAction("全选")
        select_all_action.triggered.connect(self.selectAll)

        # Show menu if it has actions
        if menu.actions():
            menu.exec_(self.mapToGlobal(position))

    def copy_selected_text(self):
        """Copy selected text"""
        self.copy()
