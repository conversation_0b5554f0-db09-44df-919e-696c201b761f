# AWS 实例创建工具

这是一个基于PyQt5的AWS实例创建工具，支持EC2和Lightsail实例的创建和管理。

## 功能特性

### EC2功能
- 查看和管理EC2实例
- 创建新的EC2实例
- 支持多种AMI（Amazon Linux 2、Ubuntu 20.04、Windows Server 2019）
- 支持多种实例类型（t2.micro、t2.small、t2.medium等）
- 自动创建全开放安全组
- 保存配置为模板

### Lightsail功能
- 查看和管理Lightsail实例
- 创建新的Lightsail实例
- 支持多种操作系统（Ubuntu 24.04、Amazon Linux 2、CentOS 7、Windows Server 2019）
- 支持多种套餐配置
- 一键快速启动功能
- 自动配置全开放防火墙规则

## 安装要求

1. Python 3.7+
2. 安装依赖包：
```bash
pip install -r requirements.txt
```

## AWS配置

在使用之前，需要配置AWS凭证。可以通过以下方式之一：

### 方法1：AWS CLI配置
```bash
aws configure
```

### 方法2：环境变量
```bash
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
export AWS_DEFAULT_REGION=us-east-1
```

### 方法3：IAM角色（如果在EC2实例上运行）
确保EC2实例具有适当的IAM角色和权限。

## 运行应用程序

```bash
python aws_instance_creator.py
```

## 使用说明

### EC2标签页
1. 选择区域
2. 点击"测试"按钮验证连接
3. 点击"EC2"按钮刷新实例列表
4. 在"启动实例"区域填写配置：
   - 实例名称
   - 实例类型
   - AMI选择
   - 密钥对（可选）
5. 点击"启动"按钮创建实例

### Lightsail标签页
1. 选择区域
2. 点击"测试"按钮验证连接
3. 在"启动实例"区域填写配置：
   - 位置
   - 套餐
   - 操作系统
   - 实例名称
4. 点击"启动"按钮创建实例
5. 或者点击"一键开启实例"使用默认配置快速创建

## 安全说明

⚠️ **重要安全提醒**：
- 此工具创建的实例默认使用全开放的安全组/防火墙规则（0.0.0.0/0）
- 这意味着所有端口对互联网开放，存在安全风险
- 建议仅在测试环境中使用
- 生产环境请根据实际需求配置适当的安全规则

## 所需权限

确保AWS用户/角色具有以下权限：

### EC2权限
- ec2:DescribeInstances
- ec2:DescribeImages
- ec2:DescribeSecurityGroups
- ec2:DescribeVpcs
- ec2:DescribeRegions
- ec2:RunInstances
- ec2:CreateSecurityGroup
- ec2:AuthorizeSecurityGroupIngress
- ec2:AuthorizeSecurityGroupEgress
- ec2:CreateTags

### Lightsail权限
- lightsail:GetInstances
- lightsail:GetRegions
- lightsail:CreateInstances
- lightsail:OpenInstancePublicPorts

## 故障排除

1. **连接失败**：检查AWS凭证配置
2. **权限错误**：确保用户具有所需权限
3. **AMI不可用**：某些AMI可能在特定区域不可用
4. **实例启动失败**：检查配额限制和实例类型可用性

## 注意事项

- 创建的实例会产生AWS费用
- 请及时删除不需要的实例以避免不必要的费用
- 建议在使用前了解AWS的定价模式
