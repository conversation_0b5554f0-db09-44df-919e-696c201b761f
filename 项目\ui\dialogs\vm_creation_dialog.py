#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟机创建对话框
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QLineEdit, QComboBox, QPushButton, QCheckBox,
                             QSpinBox, QProgressBar, QLabel, QMessageBox,
                             QGroupBox, QScrollArea, QTabWidget, QWidget,
                             QListWidget, QListWidgetItem, QSplitter)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor


class DataLoadWorker(QThread):
    """数据加载工作线程"""
    data_loaded = pyqtSignal(str, list)  # 数据类型, 数据列表
    operation_failed = pyqtSignal(str, str)  # 操作类型, 错误信息

    def __init__(self, auth_client, operation, location=None, get_all_images=False):
        super().__init__()
        self.auth_client = auth_client
        self.operation = operation
        self.location = location
        self.get_all_images = get_all_images

    def run(self):
        """执行数据加载"""
        try:
            if self.operation == 'load_locations':
                locations = self.auth_client.get_locations()
                self.data_loaded.emit('locations', locations)

            elif self.operation == 'load_vm_sizes':
                if self.location:
                    vm_sizes = self.auth_client.get_vm_sizes(self.location)
                    self.data_loaded.emit('vm_sizes', vm_sizes)
                else:
                    self.operation_failed.emit('load_vm_sizes', '未指定位置')

            elif self.operation == 'load_vm_images':
                if self.location:
                    vm_images = self.auth_client.get_vm_images(self.location, self.get_all_images)
                    self.data_loaded.emit('vm_images', vm_images)
                else:
                    self.operation_failed.emit('load_vm_images', '未指定位置')

        except Exception as e:
            self.operation_failed.emit(self.operation, str(e))


class VMCreationWorker(QThread):
    """虚拟机创建工作线程"""
    progress_update = pyqtSignal(int, str)
    creation_complete = pyqtSignal(dict)
    creation_failed = pyqtSignal(str)

    def __init__(self, auth_client, vm_config):
        super().__init__()
        self.auth_client = auth_client
        self.vm_config = vm_config

    def run(self):
        """执行虚拟机创建"""
        try:
            self.progress_update.emit(10, "正在验证配置...")

            # 验证必要参数
            required_fields = ['rg_name', 'vm_name', 'location', 'vm_size', 'username', 'password']
            for field in required_fields:
                if not self.vm_config.get(field):
                    self.creation_failed.emit(f"缺少必要参数: {field}")
                    return

            self.progress_update.emit(20, "开始创建虚拟机...")

            # 调用Azure API创建虚拟机，传递进度回调
            def progress_callback(value, message):
                self.progress_update.emit(value, message)

            result = self.auth_client.create_virtual_machine(self.vm_config, progress_callback)

            if result.get('success'):
                self.progress_update.emit(100, "虚拟机创建完成")
                self.creation_complete.emit(result)
            else:
                self.creation_failed.emit(result.get('error', '未知错误'))

        except Exception as e:
            self.creation_failed.emit(f"创建过程中发生错误: {str(e)}")


class BatchVMCreationWorker(QThread):
    """批量虚拟机创建工作线程 - 并行创建"""
    progress_update = pyqtSignal(int, str)
    vm_created = pyqtSignal(str, dict)  # 虚拟机名称, 创建结果
    vm_failed = pyqtSignal(str, str)    # 虚拟机名称, 错误信息
    batch_complete = pyqtSignal(dict)   # 批量创建完成统计

    def __init__(self, auth_client, vm_configs):
        super().__init__()
        self.auth_client = auth_client
        self.vm_configs = vm_configs
        self.completed_count = 0
        self.results = []
        self.results_lock = QThread().mutex if hasattr(QThread(), 'mutex') else None

    def run(self):
        """执行并行批量虚拟机创建"""
        import concurrent.futures
        import threading

        total_vms = len(self.vm_configs)
        self.progress_update.emit(0, f"开始并行创建 {total_vms} 台虚拟机...")

        # 使用线程池并行创建虚拟机
        max_workers = min(5, total_vms)  # 最多5个并发线程

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有创建任务
            future_to_config = {}
            for vm_config in self.vm_configs:
                future = executor.submit(self._create_single_vm, vm_config)
                future_to_config[future] = vm_config

            self.progress_update.emit(10, f"已提交 {total_vms} 个创建任务，正在并行执行...")

            # 等待所有任务完成
            for future in concurrent.futures.as_completed(future_to_config):
                vm_config = future_to_config[future]
                vm_name = vm_config['vm_name']

                try:
                    result = future.result()
                    self._handle_vm_result(vm_name, result)
                except Exception as e:
                    error_msg = f"创建过程中发生异常: {str(e)}"
                    self._handle_vm_error(vm_name, error_msg)

                # 更新进度
                self.completed_count += 1
                progress = 10 + int((self.completed_count / total_vms) * 85)
                self.progress_update.emit(
                    progress,
                    f"已完成 {self.completed_count}/{total_vms} 台虚拟机创建"
                )

        # 统计结果
        created_count = sum(1 for r in self.results if r['status'] == 'success')
        failed_count = sum(1 for r in self.results if r['status'] == 'failed')

        self.progress_update.emit(100, f"并行创建完成: 成功 {created_count} 台, 失败 {failed_count} 台")

        summary = {
            'total': total_vms,
            'created': created_count,
            'failed': failed_count,
            'results': self.results
        }

        self.batch_complete.emit(summary)

    def _create_single_vm(self, vm_config):
        """创建单个虚拟机"""
        vm_name = vm_config['vm_name']

        # 创建进度回调
        def progress_callback(value, message):
            # 发送单个虚拟机的进度更新
            self.progress_update.emit(
                -1,  # 使用-1表示这是单个虚拟机的进度
                f"{vm_name}: {message}"
            )

        return self.auth_client.create_virtual_machine(vm_config, progress_callback)

    def _handle_vm_result(self, vm_name, result):
        """处理虚拟机创建结果"""
        if result.get('success'):
            self.vm_created.emit(vm_name, result)
            self.results.append({
                'vm_name': vm_name,
                'status': 'success',
                'result': result
            })
        else:
            error_msg = result.get('error', '未知错误')
            self.vm_failed.emit(vm_name, error_msg)
            self.results.append({
                'vm_name': vm_name,
                'status': 'failed',
                'error': error_msg
            })

    def _handle_vm_error(self, vm_name, error_msg):
        """处理虚拟机创建错误"""
        self.vm_failed.emit(vm_name, error_msg)
        self.results.append({
            'vm_name': vm_name,
            'status': 'failed',
            'error': error_msg
        })


class VMCreationDialog(QDialog):
    """虚拟机创建对话框"""
    
    def __init__(self, vm_creation_widget, parent=None):
        super().__init__(parent)
        self.vm_creation_widget = vm_creation_widget
        self.worker = None  # 当前工作线程
        self.creation_worker = None  # 创建工作线程
        self.refresh_timer = None  # 刷新定时器
        self.init_ui()
        self.connect_signals()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("创建虚拟机")
        self.setModal(True)
        self.resize(1000, 800)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # 标题
        title_label = QLabel("创建新的虚拟机")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 单台创建选项卡
        self.single_tab = QWidget()
        self.tab_widget.addTab(self.single_tab, "单台创建")
        self.init_single_creation_tab()

        # 批量创建选项卡
        self.batch_tab = QWidget()
        self.tab_widget.addTab(self.batch_tab, "批量创建")
        self.init_batch_creation_tab()

    def init_single_creation_tab(self):
        """初始化单台创建选项卡"""
        layout = QVBoxLayout(self.single_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 创建子选项卡
        tab_widget = QTabWidget()

        # 基本配置选项卡
        basic_tab = self.create_basic_tab()
        tab_widget.addTab(basic_tab, "基本配置")

        # 网络配置选项卡
        network_tab = self.create_network_tab()
        tab_widget.addTab(network_tab, "网络配置")

        # 存储配置选项卡
        storage_tab = self.create_storage_tab()
        tab_widget.addTab(storage_tab, "存储配置")

        layout.addWidget(tab_widget)

        # 单台创建按钮
        single_button_layout = QHBoxLayout()
        single_button_layout.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        single_button_layout.addWidget(cancel_btn)

        self.create_btn = QPushButton("创建虚拟机")
        self.create_btn.clicked.connect(self.create_virtual_machine)
        self.create_btn.setEnabled(False)
        single_button_layout.addWidget(self.create_btn)

        layout.addLayout(single_button_layout)

    def init_batch_creation_tab(self):
        """初始化批量创建选项卡"""
        layout = QVBoxLayout(self.batch_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：配置区域
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)

        # 基本配置组
        basic_group = QGroupBox("基本配置")
        basic_layout = QFormLayout(basic_group)

        # 虚拟机名称前缀
        vm_prefix_layout = QVBoxLayout()
        self.batch_vm_prefix_edit = QLineEdit()
        self.batch_vm_prefix_edit.setPlaceholderText("例如: web-server")
        self.batch_vm_prefix_edit.textChanged.connect(self.on_batch_vm_prefix_changed)
        vm_prefix_layout.addWidget(self.batch_vm_prefix_edit)

        # 名称验证提示标签
        self.vm_prefix_hint_label = QLabel()
        self.vm_prefix_hint_label.setStyleSheet("color: gray; font-size: 10px;")
        self.vm_prefix_hint_label.setText("💡 提示：名称将自动调整为符合Azure规范")
        vm_prefix_layout.addWidget(self.vm_prefix_hint_label)

        basic_layout.addRow("虚拟机名称前缀:", vm_prefix_layout)

        # 资源组名称前缀
        self.batch_rg_prefix_edit = QLineEdit()
        self.batch_rg_prefix_edit.setPlaceholderText("例如: web-rg")
        basic_layout.addRow("资源组名称前缀:", self.batch_rg_prefix_edit)

        # 用户名
        self.batch_username_edit = QLineEdit()
        self.batch_username_edit.setText("ubuntu")
        basic_layout.addRow("用户名:", self.batch_username_edit)

        # 密码
        self.batch_password_edit = QLineEdit()
        self.batch_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        basic_layout.addRow("密码:", self.batch_password_edit)

        config_layout.addWidget(basic_group)

        # 虚拟机配置组
        vm_config_group = QGroupBox("虚拟机配置")
        vm_config_layout = QFormLayout(vm_config_group)

        # 虚拟机规格
        self.batch_vm_size_combo = QComboBox()
        vm_config_layout.addRow("虚拟机规格:", self.batch_vm_size_combo)

        # 操作系统镜像
        self.batch_vm_image_combo = QComboBox()
        vm_config_layout.addRow("操作系统镜像:", self.batch_vm_image_combo)

        # 磁盘大小
        self.batch_disk_size_spin = QSpinBox()
        self.batch_disk_size_spin.setRange(30, 1024)
        self.batch_disk_size_spin.setValue(64)
        self.batch_disk_size_spin.setSuffix(" GB")
        vm_config_layout.addRow("磁盘大小:", self.batch_disk_size_spin)

        config_layout.addWidget(vm_config_group)

        # 网络配置组
        network_group = QGroupBox("网络配置")
        network_layout = QVBoxLayout(network_group)

        self.batch_create_ip_checkbox = QCheckBox("为每台虚拟机创建公共IP")
        self.batch_create_ip_checkbox.setChecked(True)
        network_layout.addWidget(self.batch_create_ip_checkbox)

        port_layout = QHBoxLayout()
        self.batch_ssh_checkbox = QCheckBox("SSH (22)")
        self.batch_http_checkbox = QCheckBox("HTTP (80)")
        self.batch_https_checkbox = QCheckBox("HTTPS (443)")
        port_layout.addWidget(self.batch_ssh_checkbox)
        port_layout.addWidget(self.batch_http_checkbox)
        port_layout.addWidget(self.batch_https_checkbox)
        port_layout.addStretch()
        network_layout.addLayout(port_layout)

        config_layout.addWidget(network_group)
        config_layout.addStretch()

        splitter.addWidget(config_widget)

        # 右侧：区域选择和预览
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # 区域选择组
        region_group = QGroupBox("选择部署区域")
        region_layout = QVBoxLayout(region_group)

        # 可用区域列表
        self.available_regions_list = QListWidget()
        self.available_regions_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        region_layout.addWidget(QLabel("可用区域（可多选）:"))
        region_layout.addWidget(self.available_regions_list)

        # 区域选择操作按钮
        region_buttons_layout = QHBoxLayout()

        self.select_all_regions_btn = QPushButton("✅ 全选")
        self.select_all_regions_btn.clicked.connect(self.select_all_regions)
        self.select_all_regions_btn.setToolTip("选择所有可用区域")
        region_buttons_layout.addWidget(self.select_all_regions_btn)

        self.select_none_regions_btn = QPushButton("❌ 全不选")
        self.select_none_regions_btn.clicked.connect(self.select_none_regions)
        self.select_none_regions_btn.setToolTip("取消选择所有区域")
        region_buttons_layout.addWidget(self.select_none_regions_btn)

        self.invert_regions_btn = QPushButton("🔄 反选")
        self.invert_regions_btn.clicked.connect(self.invert_regions_selection)
        self.invert_regions_btn.setToolTip("反转当前选择状态")
        region_buttons_layout.addWidget(self.invert_regions_btn)

        refresh_regions_btn = QPushButton("🔄 刷新区域列表")
        refresh_regions_btn.clicked.connect(self.refresh_batch_regions)
        refresh_regions_btn.setToolTip("重新加载可用区域列表")
        region_buttons_layout.addWidget(refresh_regions_btn)

        region_layout.addLayout(region_buttons_layout)

        # 智能选择按钮（第二行）
        smart_select_layout = QHBoxLayout()

        self.select_us_regions_btn = QPushButton("🇺🇸 选择美国区域")
        self.select_us_regions_btn.clicked.connect(self.select_us_regions)
        self.select_us_regions_btn.setToolTip("选择所有美国区域")
        smart_select_layout.addWidget(self.select_us_regions_btn)

        self.select_europe_regions_btn = QPushButton("🇪🇺 选择欧洲区域")
        self.select_europe_regions_btn.clicked.connect(self.select_europe_regions)
        self.select_europe_regions_btn.setToolTip("选择所有欧洲区域")
        smart_select_layout.addWidget(self.select_europe_regions_btn)

        self.select_asia_regions_btn = QPushButton("🌏 选择亚洲区域")
        self.select_asia_regions_btn.clicked.connect(self.select_asia_regions)
        self.select_asia_regions_btn.setToolTip("选择所有亚洲区域")
        smart_select_layout.addWidget(self.select_asia_regions_btn)

        self.select_popular_regions_btn = QPushButton("⭐ 选择热门区域")
        self.select_popular_regions_btn.clicked.connect(self.select_popular_regions)
        self.select_popular_regions_btn.setToolTip("选择最常用的区域")
        smart_select_layout.addWidget(self.select_popular_regions_btn)

        region_layout.addLayout(smart_select_layout)

        right_layout.addWidget(region_group)

        # 创建预览组
        preview_group = QGroupBox("创建预览")
        preview_layout = QVBoxLayout(preview_group)

        self.batch_preview_list = QListWidget()
        preview_layout.addWidget(QLabel("将要创建的虚拟机:"))
        preview_layout.addWidget(self.batch_preview_list)

        right_layout.addWidget(preview_group)

        # 创建状态组
        status_group = QGroupBox("创建状态")
        status_layout = QVBoxLayout(status_group)

        self.batch_status_list = QListWidget()
        status_layout.addWidget(QLabel("实时创建状态:"))
        status_layout.addWidget(self.batch_status_list)

        right_layout.addWidget(status_group)

        splitter.addWidget(right_widget)
        splitter.setSizes([400, 600])

        layout.addWidget(splitter)

        # 批量创建按钮
        batch_button_layout = QHBoxLayout()
        batch_button_layout.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        batch_button_layout.addWidget(cancel_btn)

        self.batch_create_btn = QPushButton("批量创建虚拟机")
        self.batch_create_btn.clicked.connect(self.batch_create_virtual_machines)
        self.batch_create_btn.setEnabled(False)
        batch_button_layout.addWidget(self.batch_create_btn)

        self.queue_create_btn = QPushButton("加入创建队列")
        self.queue_create_btn.clicked.connect(self.add_to_creation_queue)
        self.queue_create_btn.setEnabled(False)
        batch_button_layout.addWidget(self.queue_create_btn)

        self.view_queue_btn = QPushButton("查看创建队列")
        self.view_queue_btn.clicked.connect(self.view_creation_queue)
        batch_button_layout.addWidget(self.view_queue_btn)

        layout.addLayout(batch_button_layout)

        # 连接信号
        self.available_regions_list.itemSelectionChanged.connect(self.update_batch_preview)
        self.available_regions_list.itemSelectionChanged.connect(self.update_queue_button_state)

        # 进度条（全局）
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # 状态标签（全局）
        self.status_label = QLabel("请配置虚拟机参数")
        layout.addWidget(self.status_label)

        # 连接原始组件的信号
        self.connect_signals()
        
    def create_basic_tab(self):
        """创建基本配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)
        
        # 创建新的控件而不是引用原始组件的控件
        self.rg_name_edit = QLineEdit()
        self.rg_name_edit.setPlaceholderText("输入资源组名称")

        self.vm_name_edit = QLineEdit()
        self.vm_name_edit.setPlaceholderText("输入虚拟机名称")
        # 连接虚拟机名称变化信号
        self.vm_name_edit.textChanged.connect(self.on_vm_name_changed)

        self.username_edit = QLineEdit()
        self.username_edit.setText("ubuntu")  # 默认用户名
        self.username_edit.setPlaceholderText("输入用户名")

        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setPlaceholderText("输入密码")
        
        basic_layout.addRow("资源组名称:", self.rg_name_edit)
        basic_layout.addRow("虚拟机名称:", self.vm_name_edit)
        basic_layout.addRow("用户名:", self.username_edit)
        basic_layout.addRow("密码:", self.password_edit)
        
        layout.addWidget(basic_group)
        
        # 位置和配置组
        config_group = QGroupBox("位置和配置")
        config_layout = QFormLayout(config_group)
        
        # 区域选择
        region_layout = QHBoxLayout()
        self.region_combo = QComboBox()
        self.refresh_regions_btn = QPushButton("刷新")
        self.refresh_regions_btn.clicked.connect(self.refresh_regions)
        region_layout.addWidget(self.region_combo)
        region_layout.addWidget(self.refresh_regions_btn)
        config_layout.addRow("区域:", region_layout)

        # 虚拟机配置
        size_layout = QHBoxLayout()
        self.vm_size_combo = QComboBox()
        self.refresh_sizes_btn = QPushButton("刷新")
        self.refresh_sizes_btn.clicked.connect(self.refresh_vm_sizes)
        size_layout.addWidget(self.vm_size_combo)
        size_layout.addWidget(self.refresh_sizes_btn)
        config_layout.addRow("虚拟机配置:", size_layout)

        # 镜像选择
        image_layout = QHBoxLayout()
        self.vm_image_combo = QComboBox()
        self.refresh_images_btn = QPushButton("刷新常用")
        self.refresh_images_btn.clicked.connect(self.refresh_vm_images)
        self.refresh_all_images_btn = QPushButton("刷新全部")
        self.refresh_all_images_btn.clicked.connect(self.refresh_all_vm_images)
        image_layout.addWidget(self.vm_image_combo)
        image_layout.addWidget(self.refresh_images_btn)
        image_layout.addWidget(self.refresh_all_images_btn)
        config_layout.addRow("操作系统镜像:", image_layout)
        
        layout.addWidget(config_group)
        layout.addStretch()
        
        return widget
        
    def create_network_tab(self):
        """创建网络配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 端口配置组
        port_group = QGroupBox("端口配置")
        port_layout = QVBoxLayout(port_group)
        
        port_info_label = QLabel("选择要开放的端口:")
        port_layout.addWidget(port_info_label)
        
        port_check_layout = QHBoxLayout()
        self.ssh_checkbox = QCheckBox("SSH (22)")
        self.http_checkbox = QCheckBox("HTTP (80)")
        self.https_checkbox = QCheckBox("HTTPS (443)")

        port_check_layout.addWidget(self.ssh_checkbox)
        port_check_layout.addWidget(self.http_checkbox)
        port_check_layout.addWidget(self.https_checkbox)
        port_check_layout.addStretch()

        port_layout.addLayout(port_check_layout)
        layout.addWidget(port_group)

        # 公共IP配置组
        ip_group = QGroupBox("公共IP配置")
        ip_layout = QFormLayout(ip_group)

        self.create_ip_checkbox = QCheckBox("创建公共IP")
        self.create_ip_checkbox.setChecked(True)
        self.ip_name_edit = QLineEdit()
        self.ip_name_edit.setPlaceholderText("输入公共IP名称")
        # 标记是否为自动生成的IP名称
        self.ip_name_auto_generated = True

        ip_layout.addRow(self.create_ip_checkbox)
        ip_layout.addRow("公共IP名称:", self.ip_name_edit)
        
        layout.addWidget(ip_group)
        layout.addStretch()
        
        return widget
        
    def create_storage_tab(self):
        """创建存储配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 磁盘配置组
        disk_group = QGroupBox("磁盘配置")
        disk_layout = QFormLayout(disk_group)

        self.disk_size_spin = QSpinBox()
        self.disk_size_spin.setRange(30, 1024)
        self.disk_size_spin.setValue(64)
        self.disk_size_spin.setSuffix(" GB")
        disk_layout.addRow("OS磁盘大小:", self.disk_size_spin)
        
        layout.addWidget(disk_group)
        layout.addStretch()
        
        return widget
        
    def connect_signals(self):
        """连接信号"""
        # 从原始组件获取认证信息和位置数据
        if hasattr(self.vm_creation_widget, 'auth_info'):
            self.auth_info = self.vm_creation_widget.auth_info
            if hasattr(self.vm_creation_widget, 'locations'):
                self.locations = self.vm_creation_widget.locations
            else:
                self.locations = []
            self.update_region_combo()

        # 连接信号
        self.region_combo.currentTextChanged.connect(self.on_region_changed)

        # 连接文本框变化信号以检查创建按钮状态
        self.rg_name_edit.textChanged.connect(self.check_create_button_state)
        self.vm_name_edit.textChanged.connect(self.check_create_button_state)
        self.username_edit.textChanged.connect(self.check_create_button_state)
        self.password_edit.textChanged.connect(self.check_create_button_state)
        self.region_combo.currentTextChanged.connect(self.check_create_button_state)
        self.vm_size_combo.currentTextChanged.connect(self.check_create_button_state)
        self.vm_image_combo.currentTextChanged.connect(self.check_create_button_state)

        # 连接IP名称手动编辑信号
        self.ip_name_edit.textChanged.connect(self.on_ip_name_manually_changed)

    def check_create_button_state(self):
        """检查创建按钮状态"""
        can_create = (
            bool(self.rg_name_edit.text().strip()) and
            bool(self.vm_name_edit.text().strip()) and
            bool(self.username_edit.text().strip()) and
            bool(self.password_edit.text().strip()) and
            bool(self.region_combo.currentData()) and
            bool(self.vm_size_combo.currentData()) and
            bool(self.vm_image_combo.currentData())
        )

        self.create_btn.setEnabled(can_create)

    def on_vm_name_changed(self):
        """虚拟机名称变化处理"""
        vm_name = self.vm_name_edit.text().strip()

        if vm_name:
            # 自动生成资源组名称
            rg_name = f"{vm_name}-rg"
            self.rg_name_edit.setText(rg_name)

            # 如果IP名称是自动生成的，则更新IP名称
            if self.ip_name_auto_generated:
                ip_name = f"{vm_name}-ip"
                # 临时断开信号连接，避免触发手动编辑标记
                self.ip_name_edit.textChanged.disconnect()
                self.ip_name_edit.setText(ip_name)
                self.ip_name_edit.textChanged.connect(self.on_ip_name_manually_changed)
        else:
            # 如果虚拟机名称为空，清空相关字段
            self.rg_name_edit.clear()
            if self.ip_name_auto_generated:
                self.ip_name_edit.textChanged.disconnect()
                self.ip_name_edit.clear()
                self.ip_name_edit.textChanged.connect(self.on_ip_name_manually_changed)

    def on_ip_name_manually_changed(self):
        """IP名称手动编辑处理"""
        # 如果用户手动编辑了IP名称，则标记为非自动生成
        vm_name = self.vm_name_edit.text().strip()
        expected_ip_name = f"{vm_name}-ip" if vm_name else ""
        current_ip_name = self.ip_name_edit.text().strip()

        # 如果当前IP名称不是期望的自动生成名称，则标记为手动编辑
        if current_ip_name != expected_ip_name:
            self.ip_name_auto_generated = False
        else:
            self.ip_name_auto_generated = True
            
    def on_progress_update(self, value, message):
        """进度更新"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        
    def on_create_complete(self, message):
        """创建完成"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("虚拟机创建完成")
        
        QMessageBox.information(self, "创建成功", message)
        self.close()
        
    def on_operation_failed(self, error_msg):
        """操作失败"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("操作失败")
        
        QMessageBox.critical(self, "错误", error_msg)
        
    def create_virtual_machine(self):
        """创建虚拟机"""
        # 验证输入
        if not self.rg_name_edit.text().strip():
            QMessageBox.warning(self, "警告", "请输入资源组名称")
            return

        if not self.vm_name_edit.text().strip():
            QMessageBox.warning(self, "警告", "请输入虚拟机名称")
            return

        if not self.username_edit.text().strip():
            QMessageBox.warning(self, "警告", "请输入用户名")
            return

        if not self.password_edit.text().strip():
            QMessageBox.warning(self, "警告", "请输入密码")
            return

        if not self.region_combo.currentData():
            QMessageBox.warning(self, "警告", "请选择区域")
            return

        if not self.vm_size_combo.currentData():
            QMessageBox.warning(self, "警告", "请选择虚拟机配置")
            return

        if not self.vm_image_combo.currentData():
            QMessageBox.warning(self, "警告", "请选择操作系统镜像")
            return

        if not hasattr(self, 'auth_info') or not self.auth_info:
            QMessageBox.warning(self, "警告", "认证信息不可用")
            return

        # 确认创建
        reply = QMessageBox.question(
            self, '确认创建',
            f'确定要创建虚拟机 "{self.vm_name_edit.text()}" 吗？\n\n'
            f'资源组: {self.rg_name_edit.text()}\n'
            f'区域: {self.region_combo.currentText()}\n'
            f'配置: {self.vm_size_combo.currentText()}\n'
            f'镜像: {self.vm_image_combo.currentText()}',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # 收集配置信息
        vm_config = {
            'rg_name': self.rg_name_edit.text().strip(),
            'vm_name': self.vm_name_edit.text().strip(),
            'location': self.region_combo.currentData(),
            'vm_size': self.vm_size_combo.currentData(),
            'vm_image': self.vm_image_combo.currentData(),
            'username': self.username_edit.text().strip(),
            'password': self.password_edit.text().strip(),
            'open_ssh': self.ssh_checkbox.isChecked(),
            'open_http': self.http_checkbox.isChecked(),
            'open_https': self.https_checkbox.isChecked(),
            'create_public_ip': self.create_ip_checkbox.isChecked(),
            'public_ip_name': self.ip_name_edit.text().strip() or f"{self.vm_name_edit.text().strip()}-ip",
            'disk_size': self.disk_size_spin.value()
        }

        # 停止之前的创建线程（如果有）
        self.stop_creation_worker()

        # 开始创建
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.create_btn.setEnabled(False)
        self.status_label.setText("正在创建虚拟机...")

        auth_client = self.auth_info.get('auth_client')
        self.creation_worker = VMCreationWorker(auth_client, vm_config)
        self.creation_worker.progress_update.connect(self.on_creation_progress)
        self.creation_worker.creation_complete.connect(self.on_creation_complete)
        self.creation_worker.creation_failed.connect(self.on_creation_failed)
        self.creation_worker.finished.connect(self.on_creation_worker_finished)
        self.creation_worker.start()

    def on_creation_worker_finished(self):
        """创建工作线程完成"""
        if self.creation_worker:
            self.creation_worker.deleteLater()
            self.creation_worker = None

    def update_region_combo(self):
        """更新区域下拉框"""
        self.region_combo.clear()
        if hasattr(self, 'locations'):
            for location in self.locations:
                self.region_combo.addItem(
                    location.get('displayName', ''),
                    location.get('name', '')
                )

    def refresh_regions(self):
        """刷新区域列表"""
        if not hasattr(self, 'auth_info') or not self.auth_info:
            QMessageBox.warning(self, "警告", "请先完成Azure认证")
            return

        auth_client = self.auth_info.get('auth_client')
        if not auth_client:
            QMessageBox.warning(self, "错误", "认证客户端不可用")
            return

        # 停止之前的工作线程
        self.stop_worker()

        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(30)
        self.status_label.setText("正在刷新区域列表...")
        self.refresh_regions_btn.setEnabled(False)

        # 启动工作线程
        self.worker = DataLoadWorker(auth_client, 'load_locations')
        self.worker.data_loaded.connect(self.on_data_loaded)
        self.worker.operation_failed.connect(self.on_operation_failed)
        self.worker.finished.connect(self.on_worker_finished)
        self.worker.start()

    def refresh_vm_sizes(self):
        """刷新虚拟机规格"""
        current_region = self.region_combo.currentData()
        if not current_region:
            QMessageBox.warning(self, "警告", "请先选择区域")
            return

        if not hasattr(self, 'auth_info') or not self.auth_info:
            QMessageBox.warning(self, "警告", "请先完成Azure认证")
            return

        auth_client = self.auth_info.get('auth_client')
        if not auth_client:
            QMessageBox.warning(self, "错误", "认证客户端不可用")
            return

        # 停止之前的工作线程
        self.stop_worker()

        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(30)
        self.status_label.setText("正在刷新虚拟机规格...")
        self.refresh_sizes_btn.setEnabled(False)

        # 启动工作线程
        self.worker = DataLoadWorker(auth_client, 'load_vm_sizes', current_region)
        self.worker.data_loaded.connect(self.on_data_loaded)
        self.worker.operation_failed.connect(self.on_operation_failed)
        self.worker.finished.connect(self.on_worker_finished)
        self.worker.start()

    def refresh_vm_images(self):
        """刷新虚拟机镜像"""
        current_region = self.region_combo.currentData()
        if not current_region:
            QMessageBox.warning(self, "警告", "请先选择区域")
            return

        if not hasattr(self, 'auth_info') or not self.auth_info:
            QMessageBox.warning(self, "警告", "请先完成Azure认证")
            return

        auth_client = self.auth_info.get('auth_client')
        if not auth_client:
            QMessageBox.warning(self, "错误", "认证客户端不可用")
            return

        # 停止之前的工作线程
        self.stop_worker()

        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(30)
        self.status_label.setText("正在刷新虚拟机镜像...")
        self.refresh_images_btn.setEnabled(False)

        # 启动工作线程
        self.worker = DataLoadWorker(auth_client, 'load_vm_images', current_region, get_all_images=False)
        self.worker.data_loaded.connect(self.on_data_loaded)
        self.worker.operation_failed.connect(self.on_operation_failed)
        self.worker.finished.connect(self.on_worker_finished)
        self.worker.start()

    def refresh_all_vm_images(self):
        """刷新所有虚拟机镜像"""
        current_region = self.region_combo.currentData()
        if not current_region:
            QMessageBox.warning(self, "警告", "请先选择区域")
            return

        if not hasattr(self, 'auth_info') or not self.auth_info:
            QMessageBox.warning(self, "警告", "请先完成Azure认证")
            return

        auth_client = self.auth_info.get('auth_client')
        if not auth_client:
            QMessageBox.warning(self, "错误", "认证客户端不可用")
            return

        # 警告用户这可能需要较长时间
        reply = QMessageBox.question(
            self, '确认操作',
            '获取所有Azure镜像可能需要较长时间（几分钟），确定要继续吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # 停止之前的工作线程
        self.stop_worker()

        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(30)
        self.status_label.setText("正在获取所有虚拟机镜像，请耐心等待...")
        self.refresh_images_btn.setEnabled(False)
        self.refresh_all_images_btn.setEnabled(False)

        # 启动工作线程，获取所有镜像
        self.worker = DataLoadWorker(auth_client, 'load_vm_images', current_region, get_all_images=True)
        self.worker.data_loaded.connect(self.on_data_loaded)
        self.worker.operation_failed.connect(self.on_operation_failed)
        self.worker.finished.connect(self.on_worker_finished)
        self.worker.start()

    def update_vm_size_combo(self, vm_sizes):
        """更新虚拟机规格下拉框"""
        self.vm_size_combo.clear()
        for size in vm_sizes:
            display_text = f"{size.get('name')} ({size.get('numberOfCores')}核, {size.get('memoryInMB')}MB内存)"
            self.vm_size_combo.addItem(display_text, size.get('name'))

    def update_vm_image_combo(self, vm_images):
        """更新虚拟机镜像下拉框"""
        self.vm_image_combo.clear()
        for image in vm_images:
            self.vm_image_combo.addItem(image.get('displayName'), image)

    def on_region_changed(self):
        """区域变化处理"""
        try:
            # 当区域变化时，清空虚拟机规格和镜像选择
            self.vm_size_combo.clear()
            self.vm_image_combo.clear()

            # 停止当前的定时器（如果有）
            if hasattr(self, 'refresh_timer') and self.refresh_timer and self.refresh_timer.isActive():
                self.refresh_timer.stop()

            # 自动刷新该区域的虚拟机规格（但不要同时启动多个线程）
            current_region = self.region_combo.currentData()
            if current_region and hasattr(self, 'auth_info') and self.auth_info:
                # 先停止当前线程，然后延迟启动新的刷新操作
                self.stop_worker()
                # 使用QTimer延迟执行，避免快速切换时的线程冲突
                self.refresh_timer = QTimer()
                self.refresh_timer.setSingleShot(True)
                self.refresh_timer.timeout.connect(self.delayed_refresh_on_region_change)
                self.refresh_timer.start(200)  # 延迟200ms
        except Exception as e:
            print(f"区域变化处理时发生错误: {str(e)}")

    def delayed_refresh_on_region_change(self):
        """延迟执行区域变化后的刷新操作"""
        try:
            current_region = self.region_combo.currentData()
            if current_region and hasattr(self, 'auth_info') and self.auth_info:
                self.refresh_vm_sizes()
        except Exception as e:
            print(f"延迟刷新时发生错误: {str(e)}")
            # 重置UI状态
            self.progress_bar.setVisible(False)
            self.refresh_sizes_btn.setEnabled(True)
            self.refresh_images_btn.setEnabled(True)

    def stop_worker(self):
        """停止当前工作线程"""
        if self.worker:
            if self.worker.isRunning():
                self.worker.terminate()
                self.worker.wait(1000)  # 等待最多1秒
            self.worker.deleteLater()
            self.worker = None

    def stop_creation_worker(self):
        """停止创建工作线程"""
        if self.creation_worker:
            if self.creation_worker.isRunning():
                self.creation_worker.terminate()
                self.creation_worker.wait(1000)  # 等待最多1秒
            self.creation_worker.deleteLater()
            self.creation_worker = None

    def on_worker_finished(self):
        """工作线程完成"""
        if self.worker:
            self.worker.deleteLater()
            self.worker = None

    def on_data_loaded(self, data_type, data):
        """数据加载完成处理"""
        self.progress_bar.setValue(100)

        if data_type == 'locations':
            self.locations = data
            self.update_region_combo()
            self.status_label.setText(f"区域列表刷新完成，共 {len(data)} 个区域")
            self.refresh_regions_btn.setEnabled(True)

        elif data_type == 'vm_sizes':
            self.update_vm_size_combo(data)
            self.status_label.setText(f"虚拟机规格刷新完成，共 {len(data)} 种规格")
            self.refresh_sizes_btn.setEnabled(True)

        elif data_type == 'vm_images':
            self.update_vm_image_combo(data)
            self.status_label.setText(f"虚拟机镜像刷新完成，共 {len(data)} 个镜像")
            self.refresh_images_btn.setEnabled(True)
            self.refresh_all_images_btn.setEnabled(True)

        self.progress_bar.setVisible(False)

    def on_operation_failed(self, operation, error_msg):
        """操作失败处理"""
        self.progress_bar.setVisible(False)

        if operation == 'load_locations':
            self.refresh_regions_btn.setEnabled(True)
            self.status_label.setText("刷新区域列表失败")
        elif operation == 'load_vm_sizes':
            self.refresh_sizes_btn.setEnabled(True)
            self.status_label.setText("刷新虚拟机规格失败")
        elif operation == 'load_vm_images':
            self.refresh_images_btn.setEnabled(True)
            self.refresh_all_images_btn.setEnabled(True)
            self.status_label.setText("刷新虚拟机镜像失败")

        QMessageBox.critical(self, "错误", f"操作失败: {error_msg}")

    def on_creation_progress(self, value, message):
        """创建进度更新"""
        self.progress_bar.setVisible(True)  # 确保进度条可见
        self.progress_bar.setValue(value)
        self.status_label.setText(message)

    def on_creation_complete(self, result):
        """虚拟机创建完成"""
        self.progress_bar.setVisible(False)
        self.create_btn.setEnabled(True)
        self.status_label.setText("虚拟机创建完成")

        # 显示创建结果
        vm_name = result.get('vm_name', 'Unknown')
        resource_group = result.get('resource_group', 'Unknown')
        location = result.get('location', 'Unknown')
        public_ip = result.get('public_ip', '无')

        success_msg = f"""虚拟机创建成功！

虚拟机名称: {vm_name}
资源组: {resource_group}
位置: {location}
公共IP: {public_ip}

您可以通过Azure门户或SSH/RDP连接到虚拟机。"""

        QMessageBox.information(self, "创建成功", success_msg)
        self.close()

    def on_creation_failed(self, error_msg):
        """虚拟机创建失败"""
        self.progress_bar.setVisible(False)
        self.create_btn.setEnabled(True)
        self.status_label.setText("虚拟机创建失败")

        QMessageBox.critical(self, "创建失败", f"虚拟机创建失败:\n\n{error_msg}")

    def closeEvent(self, event):
        """对话框关闭事件"""
        try:
            # 停止定时器
            if hasattr(self, 'refresh_timer') and self.refresh_timer and self.refresh_timer.isActive():
                self.refresh_timer.stop()

            # 停止所有工作线程
            self.stop_worker()
            self.stop_creation_worker()
        except Exception as e:
            print(f"关闭对话框时发生错误: {str(e)}")
        finally:
            event.accept()

    def on_batch_vm_prefix_changed(self):
        """批量虚拟机名称前缀变化处理"""
        vm_prefix = self.batch_vm_prefix_edit.text().strip()

        if vm_prefix:
            # 自动生成资源组前缀
            rg_prefix = f"{vm_prefix}-rg"
            self.batch_rg_prefix_edit.setText(rg_prefix)

            # 显示名称验证提示
            self._update_vm_prefix_hint(vm_prefix)
        else:
            self.vm_prefix_hint_label.setText("💡 提示：名称将自动调整为符合Azure规范")
            self.vm_prefix_hint_label.setStyleSheet("color: gray; font-size: 10px;")

        self.update_batch_preview()

    def _update_vm_prefix_hint(self, prefix):
        """更新虚拟机前缀提示"""
        try:
            # 生成示例名称
            example_name = self._generate_valid_vm_name(prefix, 1)

            # 检查是否需要调整
            original_name = f"{prefix}-01"

            if example_name == original_name.lower():
                # 名称有效，只是转换为小写
                self.vm_prefix_hint_label.setText(f"✅ 示例名称: {example_name}")
                self.vm_prefix_hint_label.setStyleSheet("color: green; font-size: 10px;")
            elif example_name != original_name:
                # 名称被调整
                self.vm_prefix_hint_label.setText(f"⚠️ 将调整为: {example_name} (原: {original_name})")
                self.vm_prefix_hint_label.setStyleSheet("color: orange; font-size: 10px;")
            else:
                # 名称有效
                self.vm_prefix_hint_label.setText(f"✅ 示例名称: {example_name}")
                self.vm_prefix_hint_label.setStyleSheet("color: green; font-size: 10px;")

        except Exception as e:
            self.vm_prefix_hint_label.setText("❌ 名称格式有误，将使用默认格式")
            self.vm_prefix_hint_label.setStyleSheet("color: red; font-size: 10px;")

    def refresh_batch_regions(self):
        """刷新批量创建的区域列表"""
        if not hasattr(self, 'auth_info') or not self.auth_info:
            QMessageBox.warning(self, "警告", "请先完成Azure认证")
            return

        auth_client = self.auth_info.get('auth_client')
        if not auth_client:
            QMessageBox.warning(self, "错误", "认证客户端不可用")
            return

        try:
            locations = auth_client.get_locations()
            self.available_regions_list.clear()

            for location in locations:
                item = QListWidgetItem(f"{location.get('displayName')} ({location.get('name')})")
                item.setData(Qt.ItemDataRole.UserRole, location.get('name'))
                self.available_regions_list.addItem(item)

            self.status_label.setText(f"已加载 {len(locations)} 个可用区域")

            # 同时更新批量创建的虚拟机规格和镜像
            self.update_batch_vm_options(locations[0].get('name') if locations else None)

            # 更新区域选择按钮状态
            self.update_region_buttons_state()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"刷新区域列表失败: {str(e)}")

    def select_all_regions(self):
        """选择所有区域"""
        try:
            for i in range(self.available_regions_list.count()):
                item = self.available_regions_list.item(i)
                if item:
                    item.setSelected(True)

            selected_count = self.available_regions_list.count()
            self.status_label.setText(f"已选择所有 {selected_count} 个区域")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"全选区域失败: {str(e)}")

    def select_none_regions(self):
        """取消选择所有区域"""
        try:
            self.available_regions_list.clearSelection()
            self.status_label.setText("已取消选择所有区域")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"取消选择失败: {str(e)}")

    def invert_regions_selection(self):
        """反转区域选择状态"""
        try:
            selected_count = 0
            total_count = self.available_regions_list.count()

            for i in range(total_count):
                item = self.available_regions_list.item(i)
                if item:
                    # 反转选择状态
                    item.setSelected(not item.isSelected())
                    if item.isSelected():
                        selected_count += 1

            self.status_label.setText(f"反选完成，当前选择 {selected_count}/{total_count} 个区域")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"反选区域失败: {str(e)}")

    def update_region_buttons_state(self):
        """更新区域选择按钮状态"""
        try:
            has_regions = self.available_regions_list.count() > 0

            self.select_all_regions_btn.setEnabled(has_regions)
            self.select_none_regions_btn.setEnabled(has_regions)
            self.invert_regions_btn.setEnabled(has_regions)

            # 智能选择按钮
            self.select_us_regions_btn.setEnabled(has_regions)
            self.select_europe_regions_btn.setEnabled(has_regions)
            self.select_asia_regions_btn.setEnabled(has_regions)
            self.select_popular_regions_btn.setEnabled(has_regions)

        except Exception as e:
            print(f"更新区域按钮状态失败: {str(e)}")

    def select_us_regions(self):
        """选择美国区域"""
        us_keywords = ['us', 'central', 'east', 'west', 'north', 'south']
        self._select_regions_by_keywords(us_keywords, "美国")

    def select_europe_regions(self):
        """选择欧洲区域"""
        europe_keywords = ['europe', 'uk', 'france', 'germany', 'norway', 'sweden',
                          'switzerland', 'austria', 'italy', 'poland', 'spain']
        self._select_regions_by_keywords(europe_keywords, "欧洲")

    def select_asia_regions(self):
        """选择亚洲区域"""
        asia_keywords = ['asia', 'japan', 'korea', 'india', 'indonesia', 'malaysia',
                        'australia', 'newzealand']
        self._select_regions_by_keywords(asia_keywords, "亚洲")

    def select_popular_regions(self):
        """选择热门区域"""
        popular_regions = ['eastus', 'westus2', 'centralus', 'westeurope', 'northeurope',
                          'southeastasia', 'eastasia', 'japaneast', 'australiaeast']
        self._select_regions_by_names(popular_regions, "热门")

    def _select_regions_by_keywords(self, keywords, region_type):
        """根据关键词选择区域"""
        try:
            selected_count = 0
            total_count = self.available_regions_list.count()

            # 先清除所有选择
            self.available_regions_list.clearSelection()

            for i in range(total_count):
                item = self.available_regions_list.item(i)
                if item:
                    region_name = item.data(Qt.ItemDataRole.UserRole) or ''
                    region_display = item.text().lower()

                    # 检查是否包含关键词
                    for keyword in keywords:
                        if keyword.lower() in region_name.lower() or keyword.lower() in region_display:
                            item.setSelected(True)
                            selected_count += 1
                            break

            self.status_label.setText(f"已选择 {selected_count} 个{region_type}区域")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"选择{region_type}区域失败: {str(e)}")

    def _select_regions_by_names(self, region_names, region_type):
        """根据区域名称选择区域"""
        try:
            selected_count = 0
            total_count = self.available_regions_list.count()

            # 先清除所有选择
            self.available_regions_list.clearSelection()

            for i in range(total_count):
                item = self.available_regions_list.item(i)
                if item:
                    region_name = item.data(Qt.ItemDataRole.UserRole) or ''

                    # 检查是否在指定列表中
                    if region_name.lower() in [name.lower() for name in region_names]:
                        item.setSelected(True)
                        selected_count += 1

            self.status_label.setText(f"已选择 {selected_count} 个{region_type}区域")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"选择{region_type}区域失败: {str(e)}")

    def update_batch_vm_options(self, sample_location):
        """更新批量创建的虚拟机选项"""
        if not sample_location or not hasattr(self, 'auth_info') or not self.auth_info:
            return

        auth_client = self.auth_info.get('auth_client')
        if not auth_client:
            return

        try:
            # 获取虚拟机规格
            vm_sizes = auth_client.get_vm_sizes(sample_location)
            self.batch_vm_size_combo.clear()
            for size in vm_sizes:
                display_text = f"{size.get('name')} ({size.get('numberOfCores')}核, {size.get('memoryInMB')}MB内存)"
                self.batch_vm_size_combo.addItem(display_text, size.get('name'))

            # 获取虚拟机镜像
            vm_images = auth_client.get_vm_images(sample_location, get_all_images=False)
            self.batch_vm_image_combo.clear()
            for image in vm_images:
                self.batch_vm_image_combo.addItem(image.get('displayName'), image)

        except Exception as e:
            print(f"更新批量创建选项失败: {str(e)}")

    def update_batch_preview(self):
        """更新批量创建预览"""
        self.batch_preview_list.clear()

        vm_prefix = self.batch_vm_prefix_edit.text().strip()
        rg_prefix = self.batch_rg_prefix_edit.text().strip()

        if not vm_prefix:
            self.batch_create_btn.setEnabled(False)
            return

        selected_regions = []
        for i in range(self.available_regions_list.count()):
            item = self.available_regions_list.item(i)
            if item.isSelected():
                region_name = item.data(Qt.ItemDataRole.UserRole)
                region_display = item.text()
                selected_regions.append((region_name, region_display))

        if not selected_regions:
            self.batch_create_btn.setEnabled(False)
            return

        # 生成预览列表
        for i, (region_name, region_display) in enumerate(selected_regions, 1):
            # 确保虚拟机名称符合Azure命名规范
            vm_name = self._generate_valid_vm_name(vm_prefix, i)
            rg_name = f"{rg_prefix}-{i:02d}" if rg_prefix else f"{vm_prefix}-rg-{i:02d}"

            preview_text = f"虚拟机: {vm_name} | 资源组: {rg_name} | 区域: {region_display}"
            item = QListWidgetItem(preview_text)
            item.setData(Qt.ItemDataRole.UserRole, {
                'vm_name': vm_name,
                'rg_name': rg_name,
                'region_name': region_name,
                'region_display': region_display
            })
            self.batch_preview_list.addItem(item)

        # 检查是否可以创建
        can_create = (
            bool(vm_prefix) and
            bool(self.batch_username_edit.text().strip()) and
            bool(self.batch_password_edit.text().strip()) and
            bool(selected_regions) and
            bool(self.batch_vm_size_combo.currentData()) and
            bool(self.batch_vm_image_combo.currentData())
        )

        self.batch_create_btn.setEnabled(can_create)
        self.queue_create_btn.setEnabled(can_create)

    def batch_create_virtual_machines(self):
        """批量创建虚拟机"""
        # 收集所有虚拟机配置
        vm_configs = []

        for i in range(self.batch_preview_list.count()):
            item = self.batch_preview_list.item(i)
            vm_data = item.data(Qt.ItemDataRole.UserRole)

            config = {
                'rg_name': vm_data['rg_name'],
                'vm_name': vm_data['vm_name'],
                'location': vm_data['region_name'],
                'vm_size': self.batch_vm_size_combo.currentData(),
                'vm_image': self.batch_vm_image_combo.currentData(),
                'username': self.batch_username_edit.text().strip(),
                'password': self.batch_password_edit.text().strip(),
                'open_ssh': self.batch_ssh_checkbox.isChecked(),
                'open_http': self.batch_http_checkbox.isChecked(),
                'open_https': self.batch_https_checkbox.isChecked(),
                'create_public_ip': self.batch_create_ip_checkbox.isChecked(),
                'public_ip_name': f"{vm_data['vm_name']}-ip",
                'disk_size': self.batch_disk_size_spin.value()
            }
            vm_configs.append(config)

        if not vm_configs:
            QMessageBox.warning(self, "警告", "没有要创建的虚拟机")
            return

        # 确认创建
        reply = QMessageBox.question(
            self, '确认批量创建',
            f'确定要创建 {len(vm_configs)} 台虚拟机吗？\n\n'
            f'这将在以下区域创建虚拟机：\n' +
            '\n'.join([f"• {config['vm_name']} 在 {config['location']}" for config in vm_configs[:5]]) +
            (f'\n... 还有 {len(vm_configs) - 5} 台' if len(vm_configs) > 5 else ''),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # 开始批量创建
        self.start_batch_creation(vm_configs)

    def start_batch_creation(self, vm_configs):
        """开始批量创建"""
        if not hasattr(self, 'auth_info') or not self.auth_info:
            QMessageBox.warning(self, "警告", "认证信息不可用")
            return

        auth_client = self.auth_info.get('auth_client')
        if not auth_client:
            QMessageBox.warning(self, "错误", "认证客户端不可用")
            return

        # 停止之前的创建操作（如果有）
        self.stop_creation_worker()

        # 显示进度
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.batch_create_btn.setEnabled(False)
        self.status_label.setText("正在批量创建虚拟机...")

        # 清空状态列表并添加初始状态
        self.batch_status_list.clear()
        for vm_config in vm_configs:
            vm_name = vm_config['vm_name']
            location = vm_config['location']
            status_text = f"⏳ {vm_name} - 等待创建 | 区域: {location}"

            item = QListWidgetItem(status_text)
            item.setBackground(QColor(255, 255, 200))  # 浅黄色
            self.batch_status_list.addItem(item)

        # 启动批量创建工作线程
        self.creation_worker = BatchVMCreationWorker(auth_client, vm_configs)
        self.creation_worker.progress_update.connect(self.on_batch_creation_progress)
        self.creation_worker.vm_created.connect(self.on_vm_created)
        self.creation_worker.vm_failed.connect(self.on_vm_failed)
        self.creation_worker.batch_complete.connect(self.on_batch_creation_complete)
        self.creation_worker.finished.connect(self.on_creation_worker_finished)
        self.creation_worker.start()

    def on_batch_creation_progress(self, value, message):
        """批量创建进度更新"""
        if value == -1:
            # 这是单个虚拟机的进度消息，只更新状态文本
            self.status_label.setText(message)
        else:
            # 这是总体进度，更新进度条和状态
            self.progress_bar.setVisible(True)  # 确保进度条可见
            self.progress_bar.setValue(value)
            self.status_label.setText(message)

    def on_vm_created(self, vm_name, result):
        """单个虚拟机创建成功"""
        self.append_log(f"✓ 虚拟机 {vm_name} 创建成功")

        # 在状态列表中显示成功状态
        public_ip = result.get('public_ip', '无')
        location = result.get('location', '未知')
        status_text = f"✓ {vm_name} - 创建成功 | 区域: {location} | 公共IP: {public_ip}"

        item = QListWidgetItem(status_text)
        item.setBackground(QColor(200, 255, 200))  # 浅绿色
        self.batch_status_list.addItem(item)
        self.batch_status_list.scrollToBottom()

    def on_vm_failed(self, vm_name, error_msg):
        """单个虚拟机创建失败"""
        self.append_log(f"✗ 虚拟机 {vm_name} 创建失败: {error_msg}")

        # 在状态列表中显示失败状态
        status_text = f"✗ {vm_name} - 创建失败: {error_msg[:50]}{'...' if len(error_msg) > 50 else ''}"

        item = QListWidgetItem(status_text)
        item.setBackground(QColor(255, 200, 200))  # 浅红色
        item.setToolTip(f"完整错误信息: {error_msg}")
        self.batch_status_list.addItem(item)
        self.batch_status_list.scrollToBottom()

    def on_batch_creation_complete(self, summary):
        """批量创建完成"""
        self.progress_bar.setVisible(False)
        self.batch_create_btn.setEnabled(True)

        total = summary['total']
        created = summary['created']
        failed = summary['failed']

        # 显示完成消息
        if failed == 0:
            QMessageBox.information(
                self, "批量创建成功",
                f"所有 {total} 台虚拟机创建成功！\n\n"
                f"您可以在Azure门户中查看创建的虚拟机。"
            )
            self.close()
        else:
            # 显示详细结果
            success_list = []
            failed_list = []

            for result in summary['results']:
                if result['status'] == 'success':
                    success_list.append(result['vm_name'])
                else:
                    failed_list.append(f"{result['vm_name']}: {result['error']}")

            result_msg = f"批量创建完成统计:\n\n"
            result_msg += f"总计: {total} 台\n"
            result_msg += f"成功: {created} 台\n"
            result_msg += f"失败: {failed} 台\n\n"

            if success_list:
                result_msg += f"成功创建的虚拟机:\n"
                result_msg += "\n".join([f"• {vm}" for vm in success_list[:5]])
                if len(success_list) > 5:
                    result_msg += f"\n... 还有 {len(success_list) - 5} 台"
                result_msg += "\n\n"

            if failed_list:
                result_msg += f"创建失败的虚拟机:\n"
                result_msg += "\n".join([f"• {vm}" for vm in failed_list[:3]])
                if len(failed_list) > 3:
                    result_msg += f"\n... 还有 {len(failed_list) - 3} 台"

            QMessageBox.information(self, "批量创建完成", result_msg)

    def append_log(self, message):
        """添加日志消息"""
        # 这里可以添加到日志显示区域，目前只打印
        print(f"[VM Creation] {message}")

    def update_queue_button_state(self):
        """更新队列按钮状态"""
        vm_prefix = self.batch_vm_prefix_edit.text().strip()
        selected_regions = []
        for i in range(self.available_regions_list.count()):
            item = self.available_regions_list.item(i)
            if item.isSelected():
                selected_regions.append(item)

        can_queue = (
            bool(vm_prefix) and
            bool(self.batch_username_edit.text().strip()) and
            bool(self.batch_password_edit.text().strip()) and
            bool(selected_regions) and
            bool(self.batch_vm_size_combo.currentData()) and
            bool(self.batch_vm_image_combo.currentData())
        )

        self.queue_create_btn.setEnabled(can_queue)

    def add_to_creation_queue(self):
        """添加到创建队列"""
        # 收集虚拟机配置
        vm_configs = self._collect_vm_configs()

        if not vm_configs:
            QMessageBox.warning(self, "警告", "没有要创建的虚拟机")
            return

        # 获取或创建队列管理器
        queue_manager = self._get_queue_manager()
        if not queue_manager:
            return

        # 添加到队列
        batch_name = f"{self.batch_vm_prefix_edit.text().strip()}-{len(vm_configs)}台-{QTimer().remainingTime()}"
        batch_id, task_ids = queue_manager.add_batch(vm_configs, batch_name)

        # 如果队列对话框已经打开，立即刷新数据
        if hasattr(self, 'queue_dialog') and self.queue_dialog.isVisible():
            QTimer.singleShot(100, self.queue_dialog.refresh_data)

        QMessageBox.information(
            self, "添加成功",
            f"已将 {len(vm_configs)} 台虚拟机添加到并行创建队列！\n\n"
            f"批次ID: {batch_id[:8]}...\n"
            f"✨ 所有批次将并行创建，最多支持40个虚拟机同时创建\n"
            f"🚀 您可以立即继续添加更多批次，无需等待\n"
            f"📊 点击\"查看创建队列\"可以查看实时进度\n\n"
            f"提示：多个批次会同时进行，大大提高创建效率！"
        )

        # 清空当前配置，准备下一批
        self.batch_vm_prefix_edit.clear()
        self.batch_rg_prefix_edit.clear()
        self.available_regions_list.clearSelection()
        self.update_batch_preview()

    def view_creation_queue(self):
        """查看创建队列"""
        queue_manager = self._get_queue_manager()
        if not queue_manager:
            return

        # 打开队列管理对话框
        from ui.dialogs.vm_queue_dialog import VMQueueDialog

        if not hasattr(self, 'queue_dialog') or not self.queue_dialog.isVisible():
            self.queue_dialog = VMQueueDialog(queue_manager, self)
            self.queue_dialog.show()
            # 立即刷新数据以显示最新的批次
            QTimer.singleShot(200, self.queue_dialog.refresh_data)
        else:
            # 如果对话框已经打开，刷新数据并激活窗口
            self.queue_dialog.refresh_data()
            self.queue_dialog.raise_()
            self.queue_dialog.activateWindow()

    def _collect_vm_configs(self):
        """收集虚拟机配置"""
        vm_configs = []

        vm_prefix = self.batch_vm_prefix_edit.text().strip()
        rg_prefix = self.batch_rg_prefix_edit.text().strip()

        if not vm_prefix:
            return vm_configs

        selected_regions = []
        for i in range(self.available_regions_list.count()):
            item = self.available_regions_list.item(i)
            if item.isSelected():
                region_name = item.data(Qt.ItemDataRole.UserRole)
                selected_regions.append(region_name)

        if not selected_regions:
            return vm_configs

        # 生成配置列表
        for i, region_name in enumerate(selected_regions, 1):
            # 确保虚拟机名称符合Azure命名规范
            vm_name = self._generate_valid_vm_name(vm_prefix, i)
            rg_name = f"{rg_prefix}-{i:02d}" if rg_prefix else f"{vm_prefix}-rg-{i:02d}"

            config = {
                'rg_name': rg_name,
                'vm_name': vm_name,
                'location': region_name,
                'vm_size': self.batch_vm_size_combo.currentData(),
                'vm_image': self.batch_vm_image_combo.currentData(),
                'username': self.batch_username_edit.text().strip(),
                'password': self.batch_password_edit.text().strip(),
                'open_ssh': self.batch_ssh_checkbox.isChecked(),
                'open_http': self.batch_http_checkbox.isChecked(),
                'open_https': self.batch_https_checkbox.isChecked(),
                'create_public_ip': self.batch_create_ip_checkbox.isChecked(),
                'public_ip_name': f"{vm_name}-ip",
                'disk_size': self.batch_disk_size_spin.value()
            }
            vm_configs.append(config)

        return vm_configs

    def _generate_valid_vm_name(self, prefix, index):
        """生成符合Azure命名规范的虚拟机名称"""
        try:
            # Azure域名标签规范：
            # 1. 必须以小写字母开头
            # 2. 只能包含小写字母、数字和连字符
            # 3. 必须以小写字母或数字结尾
            # 4. 长度在3-63个字符之间

            # 清理前缀：转换为小写，移除无效字符
            clean_prefix = ''.join(c.lower() if c.isalnum() else '-' for c in prefix)

            # 确保以字母开头
            if not clean_prefix or not clean_prefix[0].isalpha():
                clean_prefix = f"vm-{clean_prefix}" if clean_prefix else "vm"

            # 移除开头和结尾的连字符
            clean_prefix = clean_prefix.strip('-')

            # 如果前缀为空，使用默认值
            if not clean_prefix:
                clean_prefix = "vm"

            # 生成完整名称
            vm_name = f"{clean_prefix}-{index:02d}"

            # 确保名称不以连字符结尾
            vm_name = vm_name.rstrip('-')

            # 确保长度在有效范围内
            if len(vm_name) > 63:
                # 截断前缀部分，保留索引
                max_prefix_len = 63 - len(f"-{index:02d}")
                clean_prefix = clean_prefix[:max_prefix_len]
                vm_name = f"{clean_prefix}-{index:02d}"

            # 最终验证
            if len(vm_name) < 3:
                vm_name = f"vm-{index:02d}"

            return vm_name

        except Exception as e:
            print(f"生成虚拟机名称失败: {str(e)}")
            # 返回安全的默认名称
            return f"vm-{index:02d}"

    def _get_queue_manager(self):
        """获取或创建队列管理器"""
        if not hasattr(self, 'auth_info') or not self.auth_info:
            QMessageBox.warning(self, "警告", "认证信息不可用")
            return None

        auth_client = self.auth_info.get('auth_client')
        if not auth_client:
            QMessageBox.warning(self, "错误", "认证客户端不可用")
            return None

        # 尝试从主窗口获取全局队列管理器
        main_window = self._get_main_window()
        if main_window and hasattr(main_window, 'queue_manager'):
            return main_window.queue_manager

        # 如果主窗口没有队列管理器，创建一个并设置到主窗口
        from azure_client.vm_creation_queue import VMCreationQueue
        queue_manager = VMCreationQueue(auth_client, max_workers=40)

        if main_window:
            main_window.queue_manager = queue_manager

        return queue_manager

    def _get_main_window(self):
        """获取主窗口实例"""
        # 遍历所有顶级窗口找到主窗口
        from PyQt6.QtWidgets import QApplication
        for widget in QApplication.topLevelWidgets():
            if hasattr(widget, 'queue_manager') or widget.__class__.__name__ == 'MainWindow':
                return widget
        return None
