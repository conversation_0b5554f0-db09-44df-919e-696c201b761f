#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟机创建队列管理系统
支持后台持续创建，程序关闭后仍然运行
"""

import json
import time
import uuid
import threading
import concurrent.futures
from datetime import datetime
from pathlib import Path
import pickle
import os


class VMCreationTask:
    """虚拟机创建任务"""
    
    def __init__(self, task_id, vm_config, batch_id=None):
        self.task_id = task_id
        self.vm_config = vm_config
        self.batch_id = batch_id
        self.status = 'pending'  # pending, running, completed, failed
        self.created_time = datetime.now()
        self.started_time = None
        self.completed_time = None
        self.result = None
        self.error = None
        self.progress = 0
        
    def to_dict(self):
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'vm_config': self.vm_config,
            'batch_id': self.batch_id,
            'status': self.status,
            'created_time': self.created_time.isoformat(),
            'started_time': self.started_time.isoformat() if self.started_time else None,
            'completed_time': self.completed_time.isoformat() if self.completed_time else None,
            'result': self.result,
            'error': self.error,
            'progress': self.progress
        }
        
    @classmethod
    def from_dict(cls, data):
        """从字典创建任务"""
        task = cls(data['task_id'], data['vm_config'], data['batch_id'])
        task.status = data['status']
        task.created_time = datetime.fromisoformat(data['created_time'])
        task.started_time = datetime.fromisoformat(data['started_time']) if data['started_time'] else None
        task.completed_time = datetime.fromisoformat(data['completed_time']) if data['completed_time'] else None
        task.result = data['result']
        task.error = data['error']
        task.progress = data['progress']
        return task


class VMCreationQueue:
    """虚拟机创建队列管理器"""
    
    def __init__(self, auth_client, max_workers=40, data_dir="vm_creation_data"):
        self.auth_client = auth_client
        self.max_workers = max_workers
        self.data_dir = Path(data_dir)

        self.tasks = {}  # task_id -> VMCreationTask
        self.batches = {}  # batch_id -> list of task_ids
        self.executor = None
        self.running = True
        self.lock = threading.Lock()
        self.background_thread = None
        self.active_futures = {}  # task_id -> Future对象，用于跟踪和取消任务
        self.stop_all_flag = threading.Event()  # 全局停止标志
        self.running_futures = {}  # task_id -> Future对象，用于取消任务
        self.cancelled_tasks = set()  # 被取消的任务ID集合

        # 回调函数
        self.on_task_started = None
        self.on_task_progress = None
        self.on_task_completed = None
        self.on_task_failed = None
        self.on_batch_completed = None
        self.on_data_changed = None  # 数据变更回调

        # 延迟初始化
        self._initialize_async()

    def _initialize_async(self):
        """异步初始化"""
        try:
            # 创建数据目录
            self.data_dir.mkdir(exist_ok=True)

            # 创建线程池
            self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)

            # 加载持久化数据
            self.load_data()

            # 启动后台处理线程
            self.background_thread = threading.Thread(target=self._background_processor, daemon=True)
            self.background_thread.start()

            print(f"队列管理器初始化成功，加载了 {len(self.tasks)} 个任务")

        except Exception as e:
            print(f"队列管理器初始化失败: {str(e)}")
            # 设置默认值以防止崩溃
            if not self.executor:
                self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=1)
        
    def add_batch(self, vm_configs, batch_name=None):
        """添加一批虚拟机创建任务"""
        batch_id = str(uuid.uuid4())
        task_ids = []
        
        with self.lock:
            for vm_config in vm_configs:
                task_id = str(uuid.uuid4())
                task = VMCreationTask(task_id, vm_config, batch_id)
                self.tasks[task_id] = task
                task_ids.append(task_id)
                
            self.batches[batch_id] = {
                'task_ids': task_ids,
                'name': batch_name or f"批次-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
                'created_time': datetime.now(),
                'total_tasks': len(task_ids),
                'completed_tasks': 0,
                'failed_tasks': 0
            }
            
        # 保存数据
        self.save_data()

        # 触发数据变更回调
        if self.on_data_changed:
            try:
                self.on_data_changed('batch_added', batch_id)
            except Exception as e:
                print(f"数据变更回调失败: {str(e)}")

        return batch_id, task_ids

    def delete_batch(self, batch_id):
        """删除批次及其所有任务"""
        try:
            if self.lock.acquire(timeout=2.0):
                try:
                    if batch_id not in self.batches:
                        return {'success': False, 'error': '批次不存在'}

                    batch_info = self.batches[batch_id]
                    task_ids = batch_info.get('task_ids', [])

                    # 检查是否有正在运行的任务
                    running_tasks = []
                    for task_id in task_ids:
                        if task_id in self.tasks:
                            task = self.tasks[task_id]
                            if hasattr(task, 'status') and task.status == 'running':
                                running_tasks.append(task_id)

                    if running_tasks:
                        return {
                            'success': False,
                            'error': f'批次中有 {len(running_tasks)} 个任务正在运行，无法删除'
                        }

                    # 删除所有任务
                    deleted_tasks = 0
                    for task_id in task_ids:
                        if task_id in self.tasks:
                            del self.tasks[task_id]
                            deleted_tasks += 1

                    # 删除批次
                    del self.batches[batch_id]

                    # 保存数据
                    self.save_data()

                    # 触发数据变更回调
                    if self.on_data_changed:
                        try:
                            self.on_data_changed('batch_deleted', batch_id)
                        except Exception as e:
                            print(f"数据变更回调失败: {str(e)}")

                    return {
                        'success': True,
                        'message': f'成功删除批次及其 {deleted_tasks} 个任务'
                    }

                finally:
                    self.lock.release()
            else:
                return {'success': False, 'error': '操作超时'}

        except Exception as e:
            return {'success': False, 'error': f'删除批次失败: {str(e)}'}

    def force_delete_batch(self, batch_id):
        """强制删除批次及其所有任务（包括正在运行的任务）"""
        try:
            if self.lock.acquire(timeout=2.0):
                try:
                    if batch_id not in self.batches:
                        return {'success': False, 'error': '批次不存在'}

                    batch_info = self.batches[batch_id]
                    task_ids = batch_info.get('task_ids', [])

                    # 统计任务状态
                    running_tasks = []
                    pending_tasks = []
                    completed_tasks = []
                    failed_tasks = []

                    for task_id in task_ids:
                        if task_id in self.tasks:
                            task = self.tasks[task_id]
                            if hasattr(task, 'status'):
                                if task.status == 'running':
                                    running_tasks.append(task_id)
                                elif task.status == 'pending':
                                    pending_tasks.append(task_id)
                                elif task.status == 'completed':
                                    completed_tasks.append(task_id)
                                elif task.status == 'failed':
                                    failed_tasks.append(task_id)

                    # 强制停止正在运行的任务（标记为已取消）
                    cancelled_tasks = 0
                    for task_id in running_tasks:
                        if task_id in self.tasks:
                            task = self.tasks[task_id]
                            task.status = 'cancelled'
                            task.error = '批次被强制删除'
                            task.completed_time = datetime.now()
                            cancelled_tasks += 1

                    # 删除所有任务
                    deleted_tasks = 0
                    for task_id in task_ids:
                        if task_id in self.tasks:
                            del self.tasks[task_id]
                            deleted_tasks += 1

                    # 删除批次
                    del self.batches[batch_id]

                    # 保存数据
                    self.save_data()

                    return {
                        'success': True,
                        'message': f'强制删除批次成功！\n'
                                 f'删除任务总数: {deleted_tasks}\n'
                                 f'其中取消运行中任务: {cancelled_tasks}\n'
                                 f'等待中任务: {len(pending_tasks)}\n'
                                 f'已完成任务: {len(completed_tasks)}\n'
                                 f'失败任务: {len(failed_tasks)}',
                        'cancelled_tasks': cancelled_tasks,
                        'deleted_tasks': deleted_tasks
                    }

                finally:
                    self.lock.release()
            else:
                return {'success': False, 'error': '操作超时'}

        except Exception as e:
            return {'success': False, 'error': f'强制删除批次失败: {str(e)}'}

    def delete_completed_batches(self):
        """删除所有已完成的批次"""
        try:
            if self.lock.acquire(timeout=3.0):
                try:
                    deleted_batches = 0
                    deleted_tasks = 0

                    # 找到所有已完成的批次
                    completed_batch_ids = []
                    for batch_id, batch_info in self.batches.items():
                        task_ids = batch_info.get('task_ids', [])
                        all_completed = True

                        for task_id in task_ids:
                            if task_id in self.tasks:
                                task = self.tasks[task_id]
                                if hasattr(task, 'status') and task.status not in ['completed', 'failed']:
                                    all_completed = False
                                    break

                        if all_completed and task_ids:  # 确保批次不为空
                            completed_batch_ids.append(batch_id)

                    # 删除已完成的批次
                    for batch_id in completed_batch_ids:
                        batch_info = self.batches[batch_id]
                        task_ids = batch_info.get('task_ids', [])

                        # 删除任务
                        for task_id in task_ids:
                            if task_id in self.tasks:
                                del self.tasks[task_id]
                                deleted_tasks += 1

                        # 删除批次
                        del self.batches[batch_id]
                        deleted_batches += 1

                    # 保存数据
                    if deleted_batches > 0:
                        self.save_data()

                    return {
                        'success': True,
                        'message': f'成功删除 {deleted_batches} 个已完成批次，共 {deleted_tasks} 个任务'
                    }

                finally:
                    self.lock.release()
            else:
                return {'success': False, 'error': '操作超时'}

        except Exception as e:
            return {'success': False, 'error': f'删除已完成批次失败: {str(e)}'}

    def force_clear_all_batches(self):
        """强制清空所有批次和任务"""
        try:
            if self.lock.acquire(timeout=3.0):
                try:
                    # 统计当前状态
                    total_batches = len(self.batches)
                    total_tasks = len(self.tasks)
                    running_tasks = sum(1 for task in self.tasks.values()
                                      if hasattr(task, 'status') and task.status == 'running')
                    pending_tasks = sum(1 for task in self.tasks.values()
                                      if hasattr(task, 'status') and task.status == 'pending')

                    # 强制取消所有运行中的任务
                    cancelled_tasks = 0
                    for task in self.tasks.values():
                        if hasattr(task, 'status') and task.status == 'running':
                            task.status = 'cancelled'
                            task.error = '队列被强制清空'
                            task.completed_time = datetime.now()
                            cancelled_tasks += 1

                    # 清空所有数据
                    self.tasks.clear()
                    self.batches.clear()

                    # 保存数据
                    self.save_data()

                    return {
                        'success': True,
                        'message': f'队列强制清空完成！\n'
                                 f'删除批次数: {total_batches}\n'
                                 f'删除任务数: {total_tasks}\n'
                                 f'取消运行中任务: {cancelled_tasks}\n'
                                 f'取消等待中任务: {pending_tasks}',
                        'deleted_batches': total_batches,
                        'deleted_tasks': total_tasks,
                        'cancelled_tasks': cancelled_tasks
                    }

                finally:
                    self.lock.release()
            else:
                return {'success': False, 'error': '操作超时'}

        except Exception as e:
            return {'success': False, 'error': f'强制清空队列失败: {str(e)}'}

    def stop_all_tasks(self):
        """停止所有正在运行的任务（包括已删除批次的任务）"""
        try:
            print("开始停止所有任务...")

            # 设置全局停止标志
            self.stop_all_flag.set()

            # 统计信息
            cancelled_futures = 0
            cancelled_tasks = 0

            # 取消所有活动的Future对象
            with self.lock:
                print(f"发现 {len(self.active_futures)} 个活动任务")

                for task_id, future in list(self.active_futures.items()):
                    try:
                        if not future.done():
                            future.cancel()  # 尝试取消Future
                            cancelled_futures += 1
                            print(f"取消Future任务: {task_id}")
                    except Exception as e:
                        print(f"取消Future任务失败 {task_id}: {str(e)}")

                # 标记所有运行中的任务为已取消
                for task in self.tasks.values():
                    if hasattr(task, 'status') and task.status in ['running', 'pending']:
                        task.status = 'cancelled'
                        task.error = '被手动停止操作中止'
                        task.completed_time = datetime.now()
                        cancelled_tasks += 1

            # 等待一段时间让任务响应停止信号
            print("等待任务响应停止信号...")
            time.sleep(2)

            # 强制关闭并重新创建线程池
            if self.executor:
                print("关闭线程池...")
                self.executor.shutdown(wait=False)  # 不等待，强制关闭

            # 重新创建线程池
            print("重新创建线程池...")
            self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)

            # 清除活动Future跟踪
            with self.lock:
                self.active_futures.clear()

            # 重置停止标志
            self.stop_all_flag.clear()

            # 保存数据
            self.save_data()

            print(f"停止操作完成: 取消了 {cancelled_futures} 个Future任务, {cancelled_tasks} 个数据任务")

            return {
                'success': True,
                'message': f'成功停止所有创建任务！\n'
                         f'取消Future任务: {cancelled_futures}\n'
                         f'标记取消任务: {cancelled_tasks}\n'
                         f'线程池已重置',
                'cancelled_futures': cancelled_futures,
                'cancelled_tasks': cancelled_tasks
            }

        except Exception as e:
            print(f"停止所有任务失败: {str(e)}")
            return {'success': False, 'error': f'停止所有任务失败: {str(e)}'}

    def get_active_tasks_count(self):
        """获取活动任务数量"""
        try:
            with self.lock:
                return len(self.active_futures)
        except:
            return 0
        
    def get_queue_status(self):
        """获取队列状态"""
        try:
            # 使用超时锁，避免长时间阻塞
            if self.lock.acquire(timeout=1.0):
                try:
                    pending = sum(1 for task in self.tasks.values() if hasattr(task, 'status') and task.status == 'pending')
                    running = sum(1 for task in self.tasks.values() if hasattr(task, 'status') and task.status == 'running')
                    completed = sum(1 for task in self.tasks.values() if hasattr(task, 'status') and task.status == 'completed')
                    failed = sum(1 for task in self.tasks.values() if hasattr(task, 'status') and task.status == 'failed')

                    return {
                        'total_tasks': len(self.tasks),
                        'pending': pending,
                        'running': running,
                        'completed': completed,
                        'failed': failed,
                        'batches': len(self.batches)
                    }
                finally:
                    self.lock.release()
            else:
                # 锁获取超时，返回默认值
                print("获取队列状态超时，返回默认值")
                return {
                    'total_tasks': 0,
                    'pending': 0,
                    'running': 0,
                    'completed': 0,
                    'failed': 0,
                    'batches': 0
                }
        except Exception as e:
            print(f"获取队列状态失败: {str(e)}")
            return {
                'total_tasks': 0,
                'pending': 0,
                'running': 0,
                'completed': 0,
                'failed': 0,
                'batches': 0
            }
            
    def get_batch_status(self, batch_id):
        """获取批次状态"""
        try:
            if self.lock.acquire(timeout=1.0):
                try:
                    if batch_id not in self.batches:
                        return None

                    batch_info = self.batches[batch_id].copy()
                    task_statuses = []

                    task_ids = batch_info.get('task_ids', [])
                    for task_id in task_ids:
                        if task_id and task_id in self.tasks:
                            task = self.tasks[task_id]
                            if task and hasattr(task, 'vm_config') and task.vm_config:
                                task_status = {
                                    'task_id': task_id,
                                    'vm_name': task.vm_config.get('vm_name', 'Unknown') if task.vm_config else 'Unknown',
                                    'status': getattr(task, 'status', 'unknown'),
                                    'progress': getattr(task, 'progress', 0),
                                    'error': getattr(task, 'error', '')
                                }
                                task_statuses.append(task_status)
                            else:
                                # 任务对象无效，创建默认状态
                                task_statuses.append({
                                    'task_id': task_id,
                                    'vm_name': 'Unknown',
                                    'status': 'unknown',
                                    'progress': 0,
                                    'error': 'Task data corrupted'
                                })

                    batch_info['tasks'] = task_statuses
                    return batch_info

                finally:
                    self.lock.release()
            else:
                print(f"获取批次状态超时: {batch_id}")
                return None

        except Exception as e:
            print(f"获取批次状态失败: {str(e)}")
            return None
            
    def get_all_batches(self):
        """获取所有批次信息"""
        try:
            if self.lock.acquire(timeout=2.0):
                try:
                    batches = []
                    for batch_id, batch_info in list(self.batches.items())[:20]:  # 限制批次数量
                        try:
                            # 简化批次状态获取，避免递归调用
                            batch_data = {
                                'batch_id': batch_id,
                                'name': batch_info.get('name', 'Unknown'),
                                'created_time': batch_info.get('created_time', 'Unknown'),
                                'total_tasks': batch_info.get('total_tasks', 0),
                                'completed_tasks': 0,
                                'failed_tasks': 0,
                                'running_tasks': 0,
                                'pending_tasks': 0
                            }

                            # 快速统计任务状态
                            task_ids = batch_info.get('task_ids', [])
                            for task_id in task_ids:
                                if task_id in self.tasks:
                                    task = self.tasks[task_id]
                                    if hasattr(task, 'status'):
                                        if task.status == 'completed':
                                            batch_data['completed_tasks'] += 1
                                        elif task.status == 'failed':
                                            batch_data['failed_tasks'] += 1
                                        elif task.status == 'running':
                                            batch_data['running_tasks'] += 1
                                        elif task.status == 'pending':
                                            batch_data['pending_tasks'] += 1

                            batches.append(batch_data)

                        except Exception as e:
                            print(f"处理批次 {batch_id} 时出错: {str(e)}")
                            continue

                    # 按创建时间排序
                    try:
                        return sorted(batches, key=lambda x: x['created_time'] if hasattr(x['created_time'], 'timestamp') else 0, reverse=True)
                    except:
                        return batches

                finally:
                    self.lock.release()
            else:
                print("获取批次列表超时")
                return []

        except Exception as e:
            print(f"获取所有批次失败: {str(e)}")
            return []
            
    def _background_processor(self):
        """后台处理器，持续处理队列中的任务 - 支持并行批次"""
        submitted_tasks = set()  # 跟踪已提交的任务

        while self.running:
            try:
                # 获取待处理的任务
                pending_tasks = []
                with self.lock:
                    for task in self.tasks.values():
                        if (hasattr(task, 'status') and
                            task.status == 'pending' and
                            task.task_id not in submitted_tasks):
                            pending_tasks.append(task)

                # 并行提交所有待处理任务到线程池
                for task in pending_tasks:
                    if not self.running:
                        break
                    try:
                        # 检查全局停止标志
                        if self.stop_all_flag.is_set():
                            print("检测到全局停止标志，停止提交新任务")
                            break

                        # 检查线程池是否还有容量
                        if len(submitted_tasks) < self.max_workers * 3:  # 允许队列中有更多任务
                            future = self.executor.submit(self._process_task, task)
                            submitted_tasks.add(task.task_id)

                            # 跟踪活动的Future对象
                            with self.lock:
                                self.active_futures[task.task_id] = future

                            # 添加完成回调来清理
                            def cleanup_task(task_id):
                                def callback(fut):
                                    submitted_tasks.discard(task_id)
                                    with self.lock:
                                        self.active_futures.pop(task_id, None)
                                return callback

                            future.add_done_callback(cleanup_task(task.task_id))

                            print(f"提交任务到线程池: {task.vm_config.get('vm_name', 'Unknown')} (总提交: {len(submitted_tasks)})")
                        else:
                            # 线程池已满，等待下次循环
                            break
                    except Exception as e:
                        print(f"提交任务失败: {str(e)}")
                        submitted_tasks.discard(task.task_id)

                # 等待一段时间再检查新任务
                time.sleep(1)  # 减少检查间隔，更快响应新任务

            except Exception as e:
                print(f"后台处理器错误: {str(e)}")
                time.sleep(5)
                
    def _process_task(self, task):
        """处理单个任务"""
        try:
            # 检查全局停止标志
            if self.stop_all_flag.is_set():
                print(f"任务 {task.vm_config.get('vm_name', 'Unknown')} 被全局停止标志中止")
                with self.lock:
                    task.status = 'cancelled'
                    task.error = '被全局停止操作中止'
                    task.completed_time = datetime.now()
                return

            with self.lock:
                # 再次检查任务是否还存在（可能已被删除）
                if task.task_id not in self.tasks:
                    print(f"任务 {task.vm_config.get('vm_name', 'Unknown')} 已被删除，停止执行")
                    return

                task.status = 'running'
                task.started_time = datetime.now()

            if self.on_task_started:
                self.on_task_started(task.task_id, task.vm_config['vm_name'])

            # 进度回调（带停止检查）
            def progress_callback(value, message):
                # 检查是否需要停止
                if self.stop_all_flag.is_set():
                    raise Exception("任务被全局停止操作中止")

                # 检查任务是否还存在
                with self.lock:
                    if task.task_id not in self.tasks:
                        raise Exception("任务已被删除")

                task.progress = value
                if self.on_task_progress:
                    self.on_task_progress(task.task_id, value, message)

            # 创建虚拟机
            result = self.auth_client.create_virtual_machine(task.vm_config, progress_callback)
            
            with self.lock:
                if result.get('success'):
                    task.status = 'completed'
                    task.result = result
                    task.completed_time = datetime.now()
                    
                    if self.on_task_completed:
                        self.on_task_completed(task.task_id, result)
                else:
                    task.status = 'failed'
                    task.error = result.get('error', '未知错误')
                    task.completed_time = datetime.now()
                    
                    if self.on_task_failed:
                        self.on_task_failed(task.task_id, task.error)
                        
                # 检查批次是否完成
                self._check_batch_completion(task.batch_id)
                
            # 保存数据
            self.save_data()
            
        except Exception as e:
            with self.lock:
                task.status = 'failed'
                task.error = f"处理任务时发生异常: {str(e)}"
                task.completed_time = datetime.now()
                
            if self.on_task_failed:
                self.on_task_failed(task.task_id, task.error)
                
            self.save_data()
            
    def _check_batch_completion(self, batch_id):
        """检查批次是否完成"""
        if batch_id not in self.batches:
            return
            
        batch_info = self.batches[batch_id]
        completed_count = 0
        failed_count = 0
        
        for task_id in batch_info['task_ids']:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                if task.status == 'completed':
                    completed_count += 1
                elif task.status == 'failed':
                    failed_count += 1
                    
        total_finished = completed_count + failed_count
        
        if total_finished == batch_info['total_tasks']:
            # 批次完成
            batch_info['completed_tasks'] = completed_count
            batch_info['failed_tasks'] = failed_count
            batch_info['finished_time'] = datetime.now()
            
            if self.on_batch_completed:
                self.on_batch_completed(batch_id, completed_count, failed_count)
                
    def save_data(self):
        """保存数据到文件"""
        try:
            # 保存任务数据
            tasks_file = self.data_dir / "tasks.pkl"
            with open(tasks_file, 'wb') as f:
                pickle.dump(self.tasks, f)
                
            # 保存批次数据
            batches_file = self.data_dir / "batches.pkl"
            with open(batches_file, 'wb') as f:
                pickle.dump(self.batches, f)
                
        except Exception as e:
            print(f"保存数据失败: {str(e)}")
            
    def load_data(self):
        """从文件加载数据"""
        try:
            # 确保数据目录存在
            if not self.data_dir.exists():
                print("数据目录不存在，跳过数据加载")
                return

            # 加载任务数据
            tasks_file = self.data_dir / "tasks.pkl"
            if tasks_file.exists() and tasks_file.stat().st_size > 0:
                try:
                    with open(tasks_file, 'rb') as f:
                        loaded_tasks = pickle.load(f)
                        if isinstance(loaded_tasks, dict):
                            self.tasks = loaded_tasks
                            print(f"加载了 {len(self.tasks)} 个任务")
                        else:
                            print("任务数据格式错误，重置为空")
                            self.tasks = {}
                except Exception as e:
                    print(f"加载任务数据失败: {str(e)}")
                    self.tasks = {}
            else:
                print("任务数据文件不存在或为空")
                self.tasks = {}

            # 加载批次数据
            batches_file = self.data_dir / "batches.pkl"
            if batches_file.exists() and batches_file.stat().st_size > 0:
                try:
                    with open(batches_file, 'rb') as f:
                        loaded_batches = pickle.load(f)
                        if isinstance(loaded_batches, dict):
                            self.batches = loaded_batches
                            print(f"加载了 {len(self.batches)} 个批次")
                        else:
                            print("批次数据格式错误，重置为空")
                            self.batches = {}
                except Exception as e:
                    print(f"加载批次数据失败: {str(e)}")
                    self.batches = {}
            else:
                print("批次数据文件不存在或为空")
                self.batches = {}

            # 重置运行中的任务为待处理状态
            reset_count = 0
            for task in self.tasks.values():
                if hasattr(task, 'status') and task.status == 'running':
                    task.status = 'pending'
                    reset_count += 1

            if reset_count > 0:
                print(f"重置了 {reset_count} 个运行中的任务为待处理状态")

        except Exception as e:
            print(f"加载数据时发生未知错误: {str(e)}")
            self.tasks = {}
            self.batches = {}
            
    def shutdown(self):
        """关闭队列管理器"""
        self.running = False
        # 注意：不关闭executor，让任务继续在后台运行
        self.save_data()
        print("虚拟机创建队列将继续在后台运行...")
