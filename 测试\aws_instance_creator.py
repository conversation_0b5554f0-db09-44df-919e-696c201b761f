#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import json
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, 
                             QVBoxLayout, QHBoxLayout, QLabel, QComboBox, 
                             QPushButton, QTableWidget, QTableWidgetItem,
                             QLineEdit, QTextEdit, QCheckBox, QSpinBox,
                             QGroupBox, QFormLayout, QMessageBox, QHeaderView)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QFont
import boto3
from botocore.exceptions import ClientError, NoCredentialsError

class AWSInstanceCreator(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AWS 实例创建工具")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置窗口图标（使用默认图标）
        self.setWindowIcon(self.style().standardIcon(self.style().SP_ComputerIcon))
        
        # 创建中央部件和标签页
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 创建标签页控件
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 创建EC2和Lightsail标签页
        self.create_ec2_tab()
        self.create_lightsail_tab()
        
        # AWS客户端
        self.ec2_client = None
        self.lightsail_client = None
        
    def create_ec2_tab(self):
        """创建EC2标签页"""
        ec2_widget = QWidget()
        self.tab_widget.addTab(ec2_widget, "EC2")
        
        layout = QVBoxLayout(ec2_widget)
        
        # 顶部按钮区域
        top_layout = QHBoxLayout()
        
        # EC2按钮（橙色背景）
        ec2_btn = QPushButton("EC2")
        ec2_btn.setStyleSheet("QPushButton { background-color: #ff9900; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        ec2_btn.clicked.connect(self.refresh_ec2_instances)
        top_layout.addWidget(ec2_btn)
        
        # 保存为模板按钮
        save_template_btn = QPushButton("保存为模板")
        save_template_btn.clicked.connect(self.save_ec2_template)
        top_layout.addWidget(save_template_btn)
        
        top_layout.addStretch()
        layout.addLayout(top_layout)
        
        # 检查实例标题
        layout.addWidget(QLabel("检查实例"))
        
        # 区域选择
        region_layout = QHBoxLayout()
        region_layout.addWidget(QLabel("区域:"))
        self.ec2_region_combo = QComboBox()
        self.ec2_region_combo.addItems([
            "US East (N. Virginia) 美国东部",
            "US West (Oregon) 美国西部",
            "Europe (Ireland) 欧洲",
            "Asia Pacific (Singapore) 亚太地区"
        ])
        region_layout.addWidget(self.ec2_region_combo)
        region_layout.addStretch()
        layout.addLayout(region_layout)
        
        # 测试按钮
        test_btn = QPushButton("测试")
        test_btn.setStyleSheet("QPushButton { background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        test_btn.clicked.connect(self.test_ec2_connection)
        layout.addWidget(test_btn)
        
        # 实例表格
        self.ec2_table = QTableWidget()
        self.ec2_table.setColumnCount(10)
        self.ec2_table.setHorizontalHeaderLabels([
            "名称", "实例", "状态", "实例类型", "公有IPv4", "私有IPv4", "网络IPv4", "监控", "状态", "平台"
        ])
        self.ec2_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.ec2_table)
        
        # 启动实例区域
        launch_group = QGroupBox("启动实例")
        launch_layout = QFormLayout(launch_group)
        
        # 实例配置
        self.ec2_name_edit = QLineEdit()
        self.ec2_name_edit.setPlaceholderText("实例名称")
        launch_layout.addRow("名称:", self.ec2_name_edit)
        
        self.ec2_instance_type_combo = QComboBox()
        self.ec2_instance_type_combo.addItems(["t2.micro", "t2.small", "t2.medium", "t3.micro", "t3.small"])
        launch_layout.addRow("实例类型:", self.ec2_instance_type_combo)
        
        self.ec2_ami_combo = QComboBox()
        self.ec2_ami_combo.addItems(["Amazon Linux 2", "Ubuntu 20.04", "Windows Server 2019"])
        launch_layout.addRow("AMI:", self.ec2_ami_combo)
        
        self.ec2_key_pair_edit = QLineEdit()
        self.ec2_key_pair_edit.setPlaceholderText("密钥对名称")
        launch_layout.addRow("密钥对:", self.ec2_key_pair_edit)
        
        # 启动按钮
        launch_ec2_btn = QPushButton("启动")
        launch_ec2_btn.setStyleSheet("QPushButton { background-color: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        launch_ec2_btn.clicked.connect(self.launch_ec2_instance)
        launch_layout.addRow("", launch_ec2_btn)
        
        layout.addWidget(launch_group)
        
    def create_lightsail_tab(self):
        """创建Lightsail标签页"""
        lightsail_widget = QWidget()
        self.tab_widget.addTab(lightsail_widget, "Lightsail")
        
        layout = QVBoxLayout(lightsail_widget)
        
        # 顶部按钮区域
        top_layout = QHBoxLayout()
        
        # Lightsail按钮（橙色背景）
        lightsail_btn = QPushButton("Lightsail")
        lightsail_btn.setStyleSheet("QPushButton { background-color: #ff9900; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        lightsail_btn.clicked.connect(self.refresh_lightsail_instances)
        top_layout.addWidget(lightsail_btn)
        
        # 一键开启实例按钮
        quick_start_btn = QPushButton("一键开启实例")
        quick_start_btn.setStyleSheet("QPushButton { background-color: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        quick_start_btn.clicked.connect(self.quick_start_lightsail)
        top_layout.addWidget(quick_start_btn)
        
        top_layout.addStretch()
        layout.addLayout(top_layout)
        
        # 测试配置标题
        layout.addWidget(QLabel("测试配置"))
        
        # 区域选择
        region_layout = QHBoxLayout()
        region_layout.addWidget(QLabel("区域:"))
        self.lightsail_region_combo = QComboBox()
        self.lightsail_region_combo.addItems([
            "US East (N. Virginia) 美国东部",
            "US West (Oregon) 美国西部", 
            "Europe (Ireland) 欧洲",
            "Asia Pacific (Singapore) 亚太地区"
        ])
        region_layout.addWidget(self.lightsail_region_combo)
        region_layout.addStretch()
        layout.addLayout(region_layout)
        
        # 测试按钮
        test_btn = QPushButton("测试")
        test_btn.setStyleSheet("QPushButton { background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        test_btn.clicked.connect(self.test_lightsail_connection)
        layout.addWidget(test_btn)
        
        # 启动实例区域
        launch_group = QGroupBox("启动实例")
        launch_layout = QFormLayout(launch_group)
        
        # 位置
        self.lightsail_location_combo = QComboBox()
        self.lightsail_location_combo.addItems([
            "US East (N. Virginia) 美国东部",
            "US West (Oregon) 美国西部",
            "Europe (Ireland) 欧洲", 
            "Asia Pacific (Singapore) 亚太地区"
        ])
        launch_layout.addRow("位置:", self.lightsail_location_combo)
        
        # 套餐
        self.lightsail_plan_combo = QComboBox()
        self.lightsail_plan_combo.addItems([
            "$3.50/月 (1 vCPU, 512 MB RAM)",
            "$5/月 (1 vCPU, 1 GB RAM)", 
            "$10/月 (1 vCPU, 2 GB RAM)",
            "$20/月 (2 vCPU, 4 GB RAM)"
        ])
        launch_layout.addRow("套餐:", self.lightsail_plan_combo)
        
        # 系统
        self.lightsail_os_combo = QComboBox()
        self.lightsail_os_combo.addItems(["Ubuntu 24.04", "Amazon Linux 2", "CentOS 7", "Windows Server 2019"])
        launch_layout.addRow("系统:", self.lightsail_os_combo)
        
        # 实例名称
        self.lightsail_name_edit = QLineEdit()
        self.lightsail_name_edit.setPlaceholderText("实例名称")
        launch_layout.addRow("实例名称:", self.lightsail_name_edit)
        
        # 启动按钮
        launch_lightsail_btn = QPushButton("启动")
        launch_lightsail_btn.setStyleSheet("QPushButton { background-color: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        launch_lightsail_btn.clicked.connect(self.launch_lightsail_instance)
        launch_layout.addRow("", launch_lightsail_btn)
        
        layout.addWidget(launch_group)
        
    def get_region_code(self, region_text):
        """将区域显示文本转换为AWS区域代码"""
        region_map = {
            "US East (N. Virginia) 美国东部": "us-east-1",
            "US West (Oregon) 美国西部": "us-west-2",
            "Europe (Ireland) 欧洲": "eu-west-1",
            "Asia Pacific (Singapore) 亚太地区": "ap-southeast-1"
        }
        return region_map.get(region_text, "us-east-1")
        
    def test_ec2_connection(self):
        """测试EC2连接"""
        try:
            region = self.get_region_code(self.ec2_region_combo.currentText())
            self.ec2_client = boto3.client('ec2', region_name=region)
            
            # 测试连接
            response = self.ec2_client.describe_regions()
            QMessageBox.information(self, "连接成功", "EC2连接测试成功！")
            
        except NoCredentialsError:
            QMessageBox.warning(self, "认证错误", "AWS凭证未配置，请配置AWS CLI或环境变量")
        except Exception as e:
            QMessageBox.critical(self, "连接失败", f"EC2连接失败: {str(e)}")
            
    def test_lightsail_connection(self):
        """测试Lightsail连接"""
        try:
            region = self.get_region_code(self.lightsail_region_combo.currentText())
            self.lightsail_client = boto3.client('lightsail', region_name=region)
            
            # 测试连接
            response = self.lightsail_client.get_regions()
            QMessageBox.information(self, "连接成功", "Lightsail连接测试成功！")
            
        except NoCredentialsError:
            QMessageBox.warning(self, "认证错误", "AWS凭证未配置，请配置AWS CLI或环境变量")
        except Exception as e:
            QMessageBox.critical(self, "连接失败", f"Lightsail连接失败: {str(e)}")

    def refresh_ec2_instances(self):
        """刷新EC2实例列表"""
        if not self.ec2_client:
            self.test_ec2_connection()
            if not self.ec2_client:
                return

        try:
            response = self.ec2_client.describe_instances()

            # 清空表格
            self.ec2_table.setRowCount(0)

            # 填充实例数据
            row = 0
            for reservation in response['Reservations']:
                for instance in reservation['Instances']:
                    self.ec2_table.insertRow(row)

                    # 获取实例名称
                    name = ""
                    if 'Tags' in instance:
                        for tag in instance['Tags']:
                            if tag['Key'] == 'Name':
                                name = tag['Value']
                                break

                    self.ec2_table.setItem(row, 0, QTableWidgetItem(name))
                    self.ec2_table.setItem(row, 1, QTableWidgetItem(instance['InstanceId']))
                    self.ec2_table.setItem(row, 2, QTableWidgetItem(instance['State']['Name']))
                    self.ec2_table.setItem(row, 3, QTableWidgetItem(instance['InstanceType']))
                    self.ec2_table.setItem(row, 4, QTableWidgetItem(instance.get('PublicIpAddress', '')))
                    self.ec2_table.setItem(row, 5, QTableWidgetItem(instance.get('PrivateIpAddress', '')))
                    self.ec2_table.setItem(row, 6, QTableWidgetItem(instance.get('VpcId', '')))
                    self.ec2_table.setItem(row, 7, QTableWidgetItem('基本'))
                    self.ec2_table.setItem(row, 8, QTableWidgetItem(instance['State']['Name']))
                    self.ec2_table.setItem(row, 9, QTableWidgetItem(instance.get('Platform', 'Linux')))

                    row += 1

        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取EC2实例失败: {str(e)}")

    def refresh_lightsail_instances(self):
        """刷新Lightsail实例列表"""
        if not self.lightsail_client:
            self.test_lightsail_connection()
            if not self.lightsail_client:
                return

        try:
            response = self.lightsail_client.get_instances()
            print(f"Lightsail实例数量: {len(response.get('instances', []))}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取Lightsail实例失败: {str(e)}")

    def get_ami_id(self, ami_name, region):
        """根据AMI名称获取AMI ID"""
        ami_map = {
            "Amazon Linux 2": {
                "us-east-1": "ami-0abcdef1234567890",
                "us-west-2": "ami-0abcdef1234567890",
                "eu-west-1": "ami-0abcdef1234567890",
                "ap-southeast-1": "ami-0abcdef1234567890"
            },
            "Ubuntu 20.04": {
                "us-east-1": "ami-0885b1f6bd170450c",
                "us-west-2": "ami-0892d3c7ee96c0bf7",
                "eu-west-1": "ami-0a8e758f5e873d1c1",
                "ap-southeast-1": "ami-0d058fe428540cd89"
            },
            "Windows Server 2019": {
                "us-east-1": "ami-0c02fb55956c7d316",
                "us-west-2": "ami-0ceecbb0f30a902a6",
                "eu-west-1": "ami-0cb72d2e599cffbf9",
                "ap-southeast-1": "ami-0ca5c3bd5a268e7db"
            }
        }

        # 如果没有找到预定义的AMI，尝试搜索最新的
        if ami_name in ami_map and region in ami_map[ami_name]:
            return ami_map[ami_name][region]
        else:
            # 搜索最新的AMI
            try:
                if "Amazon Linux" in ami_name:
                    filters = [
                        {'Name': 'name', 'Values': ['amzn2-ami-hvm-*']},
                        {'Name': 'owner-alias', 'Values': ['amazon']},
                        {'Name': 'state', 'Values': ['available']}
                    ]
                elif "Ubuntu" in ami_name:
                    filters = [
                        {'Name': 'name', 'Values': ['ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-*']},
                        {'Name': 'owner-id', 'Values': ['099720109477']},
                        {'Name': 'state', 'Values': ['available']}
                    ]
                elif "Windows" in ami_name:
                    filters = [
                        {'Name': 'name', 'Values': ['Windows_Server-2019-English-Full-Base-*']},
                        {'Name': 'owner-alias', 'Values': ['amazon']},
                        {'Name': 'state', 'Values': ['available']}
                    ]
                else:
                    return None

                response = self.ec2_client.describe_images(Filters=filters)
                if response['Images']:
                    # 按创建日期排序，获取最新的
                    images = sorted(response['Images'], key=lambda x: x['CreationDate'], reverse=True)
                    return images[0]['ImageId']

            except Exception as e:
                print(f"搜索AMI失败: {e}")

        return None

    def launch_ec2_instance(self):
        """启动EC2实例"""
        if not self.ec2_client:
            self.test_ec2_connection()
            if not self.ec2_client:
                return

        try:
            # 获取配置
            name = self.ec2_name_edit.text().strip()
            instance_type = self.ec2_instance_type_combo.currentText()
            ami_name = self.ec2_ami_combo.currentText()
            key_pair = self.ec2_key_pair_edit.text().strip()
            region = self.get_region_code(self.ec2_region_combo.currentText())

            if not name:
                QMessageBox.warning(self, "输入错误", "请输入实例名称")
                return

            # 获取AMI ID
            ami_id = self.get_ami_id(ami_name, region)
            if not ami_id:
                QMessageBox.warning(self, "AMI错误", f"无法找到{ami_name}的AMI")
                return

            # 创建或获取安全组（全开放）
            security_group_id = self.create_open_security_group()

            # 启动实例参数
            launch_params = {
                'ImageId': ami_id,
                'MinCount': 1,
                'MaxCount': 1,
                'InstanceType': instance_type,
                'SecurityGroupIds': [security_group_id],
                'TagSpecifications': [
                    {
                        'ResourceType': 'instance',
                        'Tags': [
                            {'Key': 'Name', 'Value': name}
                        ]
                    }
                ]
            }

            # 如果指定了密钥对，添加到参数中
            if key_pair:
                launch_params['KeyName'] = key_pair

            # 启动实例
            response = self.ec2_client.run_instances(**launch_params)

            instance_id = response['Instances'][0]['InstanceId']
            QMessageBox.information(self, "成功", f"EC2实例创建成功！\n实例ID: {instance_id}")

            # 刷新实例列表
            self.refresh_ec2_instances()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建EC2实例失败: {str(e)}")

    def create_open_security_group(self):
        """创建全开放的安全组"""
        try:
            # 检查是否已存在全开放安全组
            response = self.ec2_client.describe_security_groups(
                Filters=[
                    {'Name': 'group-name', 'Values': ['open-all-sg']}
                ]
            )

            if response['SecurityGroups']:
                return response['SecurityGroups'][0]['GroupId']

            # 获取默认VPC
            vpc_response = self.ec2_client.describe_vpcs(
                Filters=[{'Name': 'is-default', 'Values': ['true']}]
            )

            if not vpc_response['Vpcs']:
                raise Exception("未找到默认VPC")

            vpc_id = vpc_response['Vpcs'][0]['VpcId']

            # 创建安全组
            sg_response = self.ec2_client.create_security_group(
                GroupName='open-all-sg',
                Description='Open all ports security group',
                VpcId=vpc_id
            )

            security_group_id = sg_response['GroupId']

            # 添加入站规则（全开放）
            self.ec2_client.authorize_security_group_ingress(
                GroupId=security_group_id,
                IpPermissions=[
                    {
                        'IpProtocol': '-1',
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    }
                ]
            )

            # 添加出站规则（全开放）
            self.ec2_client.authorize_security_group_egress(
                GroupId=security_group_id,
                IpPermissions=[
                    {
                        'IpProtocol': '-1',
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    }
                ]
            )

            return security_group_id

        except Exception as e:
            print(f"创建安全组失败: {e}")
            # 如果创建失败，尝试使用默认安全组
            try:
                response = self.ec2_client.describe_security_groups(
                    Filters=[{'Name': 'group-name', 'Values': ['default']}]
                )
                if response['SecurityGroups']:
                    return response['SecurityGroups'][0]['GroupId']
            except:
                pass
            raise e

    def get_lightsail_blueprint(self, os_name):
        """获取Lightsail蓝图ID"""
        blueprint_map = {
            "Ubuntu 24.04": "ubuntu_24_04",
            "Amazon Linux 2": "amazon_linux_2",
            "CentOS 7": "centos_7_2009_01",
            "Windows Server 2019": "windows_server_2019"
        }
        return blueprint_map.get(os_name, "ubuntu_24_04")

    def get_lightsail_bundle(self, plan_text):
        """获取Lightsail套餐ID"""
        bundle_map = {
            "$3.50/月 (1 vCPU, 512 MB RAM)": "nano_2_0",
            "$5/月 (1 vCPU, 1 GB RAM)": "micro_2_0",
            "$10/月 (1 vCPU, 2 GB RAM)": "small_2_0",
            "$20/月 (2 vCPU, 4 GB RAM)": "medium_2_0"
        }
        return bundle_map.get(plan_text, "micro_2_0")

    def launch_lightsail_instance(self):
        """启动Lightsail实例"""
        if not self.lightsail_client:
            self.test_lightsail_connection()
            if not self.lightsail_client:
                return

        try:
            # 获取配置
            name = self.lightsail_name_edit.text().strip()
            location = self.get_region_code(self.lightsail_location_combo.currentText())
            plan = self.get_lightsail_bundle(self.lightsail_plan_combo.currentText())
            os_name = self.lightsail_os_combo.currentText()
            blueprint = self.get_lightsail_blueprint(os_name)

            if not name:
                QMessageBox.warning(self, "输入错误", "请输入实例名称")
                return

            # 创建实例
            response = self.lightsail_client.create_instances(
                instanceNames=[name],
                availabilityZone=f"{location}a",  # 使用第一个可用区
                blueprintId=blueprint,
                bundleId=plan,
                tags=[
                    {'key': 'CreatedBy', 'value': 'AWS-Instance-Creator'}
                ]
            )

            QMessageBox.information(self, "成功", f"Lightsail实例创建成功！\n实例名称: {name}")

            # 创建全开放防火墙规则
            self.create_lightsail_open_firewall(name)

            # 刷新实例列表
            self.refresh_lightsail_instances()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建Lightsail实例失败: {str(e)}")

    def create_lightsail_open_firewall(self, instance_name):
        """为Lightsail实例创建全开放防火墙规则"""
        try:
            # 等待实例启动
            import time
            time.sleep(5)

            # 开放所有端口
            self.lightsail_client.open_instance_public_ports(
                instanceName=instance_name,
                portInfo={
                    'fromPort': 0,
                    'toPort': 65535,
                    'protocol': 'tcp'
                }
            )

            self.lightsail_client.open_instance_public_ports(
                instanceName=instance_name,
                portInfo={
                    'fromPort': 0,
                    'toPort': 65535,
                    'protocol': 'udp'
                }
            )

        except Exception as e:
            print(f"设置防火墙规则失败: {e}")

    def quick_start_lightsail(self):
        """一键启动Lightsail实例"""
        # 设置默认值
        self.lightsail_name_edit.setText("quick-instance")
        self.lightsail_plan_combo.setCurrentText("$5/月 (1 vCPU, 1 GB RAM)")
        self.lightsail_os_combo.setCurrentText("Ubuntu 24.04")

        # 启动实例
        self.launch_lightsail_instance()

    def save_ec2_template(self):
        """保存EC2配置为模板"""
        try:
            template = {
                "name": self.ec2_name_edit.text(),
                "instance_type": self.ec2_instance_type_combo.currentText(),
                "ami": self.ec2_ami_combo.currentText(),
                "key_pair": self.ec2_key_pair_edit.text(),
                "region": self.ec2_region_combo.currentText()
            }

            with open("ec2_template.json", "w", encoding="utf-8") as f:
                json.dump(template, f, ensure_ascii=False, indent=2)

            QMessageBox.information(self, "成功", "EC2模板已保存到 ec2_template.json")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存模板失败: {str(e)}")


def main():
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    window = AWSInstanceCreator()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
