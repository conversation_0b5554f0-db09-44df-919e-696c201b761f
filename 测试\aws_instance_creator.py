#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import json
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget,
                             QVBoxLayout, QHBoxLayout, QLabel, QComboBox,
                             QPushButton, QTableWidget, QTableWidgetItem,
                             QLineEdit, QGroupBox, QFormLayout, QMessageBox, QHeaderView)

import boto3
from botocore.exceptions import NoCredentialsError

class AWSInstanceCreator(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AWS 实例创建工具")
        self.setGeometry(100, 100, 1200, 900)

        # 设置窗口图标（使用默认图标）
        self.setWindowIcon(self.style().standardIcon(self.style().SP_ComputerIcon))

        # 创建中央部件和标签页
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # 创建AWS凭证配置区域
        self.create_credentials_section(layout)

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 创建EC2和Lightsail标签页
        self.create_ec2_tab()
        self.create_lightsail_tab()

        # AWS客户端
        self.ec2_client = None
        self.lightsail_client = None

        # 初始化时检查现有凭证
        self.check_existing_credentials()

    def create_credentials_section(self, parent_layout):
        """创建AWS凭证配置区域"""
        # 创建凭证配置组框
        credentials_group = QGroupBox("AWS 凭证配置")
        credentials_layout = QHBoxLayout(credentials_group)

        # Access Key ID输入框
        access_key_layout = QVBoxLayout()
        access_key_layout.addWidget(QLabel("Access Key ID"))
        self.access_key_edit = QLineEdit()
        self.access_key_edit.setPlaceholderText("输入您的AWS Access Key ID")
        self.access_key_edit.textChanged.connect(self.on_credentials_changed)
        access_key_layout.addWidget(self.access_key_edit)
        credentials_layout.addLayout(access_key_layout)

        # Secret Access Key输入框
        secret_key_layout = QVBoxLayout()
        secret_key_layout.addWidget(QLabel("Secret Access Key"))
        self.secret_key_edit = QLineEdit()
        self.secret_key_edit.setPlaceholderText("输入您的AWS Secret Access Key")
        self.secret_key_edit.setEchoMode(QLineEdit.Password)
        self.secret_key_edit.textChanged.connect(self.on_credentials_changed)
        secret_key_layout.addWidget(self.secret_key_edit)
        credentials_layout.addLayout(secret_key_layout)

        # 按钮区域
        button_layout = QVBoxLayout()

        # 使用现有账户按钮
        self.use_existing_btn = QPushButton("使用现有账户")
        self.use_existing_btn.setStyleSheet("QPushButton { background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        self.use_existing_btn.clicked.connect(self.use_existing_credentials)
        button_layout.addWidget(self.use_existing_btn)

        # 添加配置用户按钮
        self.add_config_btn = QPushButton("添加配置用户")
        self.add_config_btn.setStyleSheet("QPushButton { background-color: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        self.add_config_btn.clicked.connect(self.add_configuration)
        button_layout.addWidget(self.add_config_btn)

        credentials_layout.addLayout(button_layout)

        # 状态指示器
        status_layout = QVBoxLayout()
        self.status_label = QLabel("状态：未配置")
        self.status_label.setStyleSheet("QLabel { color: #dc3545; font-weight: bold; }")
        status_layout.addWidget(self.status_label)

        # 账户信息显示
        self.account_info_label = QLabel("")
        self.account_info_label.setStyleSheet("QLabel { color: #6c757d; font-size: 12px; }")
        status_layout.addWidget(self.account_info_label)

        credentials_layout.addLayout(status_layout)

        parent_layout.addWidget(credentials_group)

    def check_existing_credentials(self):
        """检查现有的AWS凭证"""
        try:
            # 尝试使用现有凭证创建客户端
            session = boto3.Session()
            credentials = session.get_credentials()

            if credentials and credentials.access_key and credentials.secret_key:
                # 测试凭证是否有效
                try:
                    sts_client = boto3.client('sts')
                    response = sts_client.get_caller_identity()

                    # 显示账户信息
                    account_id = response.get('Account', 'Unknown')
                    user_arn = response.get('Arn', 'Unknown')

                    self.status_label.setText("状态：已连接")
                    self.status_label.setStyleSheet("QLabel { color: #28a745; font-weight: bold; }")
                    self.account_info_label.setText(f"账户ID: {account_id}\n用户: {user_arn.split('/')[-1] if '/' in user_arn else 'Unknown'}")

                    # 设置客户端
                    self.ec2_client = boto3.client('ec2', region_name='us-east-1')
                    self.lightsail_client = boto3.client('lightsail', region_name='us-east-1')

                    return True

                except Exception as e:
                    self.status_label.setText("状态：凭证无效")
                    self.status_label.setStyleSheet("QLabel { color: #dc3545; font-weight: bold; }")
                    self.account_info_label.setText(f"错误: {str(e)}")

            else:
                self.status_label.setText("状态：未配置")
                self.status_label.setStyleSheet("QLabel { color: #dc3545; font-weight: bold; }")
                self.account_info_label.setText("请配置AWS凭证")

        except Exception as e:
            self.status_label.setText("状态：检查失败")
            self.status_label.setStyleSheet("QLabel { color: #dc3545; font-weight: bold; }")
            self.account_info_label.setText(f"错误: {str(e)}")

        return False

    def on_credentials_changed(self):
        """凭证输入框内容改变时的处理"""
        access_key = self.access_key_edit.text().strip()
        secret_key = self.secret_key_edit.text().strip()

        if access_key and secret_key:
            self.add_config_btn.setEnabled(True)
        else:
            self.add_config_btn.setEnabled(False)

    def use_existing_credentials(self):
        """使用现有凭证"""
        if self.check_existing_credentials():
            QMessageBox.information(self, "成功", "已成功连接到AWS账户！")
        else:
            QMessageBox.warning(self, "失败", "未找到有效的AWS凭证，请配置凭证后重试。")

    def add_configuration(self):
        """添加新的凭证配置"""
        access_key = self.access_key_edit.text().strip()
        secret_key = self.secret_key_edit.text().strip()

        if not access_key or not secret_key:
            QMessageBox.warning(self, "输入错误", "请输入完整的Access Key ID和Secret Access Key")
            return

        # 验证凭证
        try:
            # 创建临时客户端测试凭证
            temp_session = boto3.Session(
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key
            )

            sts_client = temp_session.client('sts')
            response = sts_client.get_caller_identity()

            # 凭证有效，保存到环境变量
            import os
            os.environ['AWS_ACCESS_KEY_ID'] = access_key
            os.environ['AWS_SECRET_ACCESS_KEY'] = secret_key

            # 更新客户端
            self.ec2_client = boto3.client('ec2', region_name='us-east-1')
            self.lightsail_client = boto3.client('lightsail', region_name='us-east-1')

            # 更新状态显示
            account_id = response.get('Account', 'Unknown')
            user_arn = response.get('Arn', 'Unknown')

            self.status_label.setText("状态：已连接")
            self.status_label.setStyleSheet("QLabel { color: #28a745; font-weight: bold; }")
            self.account_info_label.setText(f"账户ID: {account_id}\n用户: {user_arn.split('/')[-1] if '/' in user_arn else 'Unknown'}")

            # 清空输入框
            self.access_key_edit.clear()
            self.secret_key_edit.clear()

            QMessageBox.information(self, "成功", "AWS凭证配置成功！")

        except Exception as e:
            QMessageBox.critical(self, "配置失败", f"AWS凭证验证失败：{str(e)}\n\n请检查：\n1. Access Key ID是否正确\n2. Secret Access Key是否正确\n3. 网络连接是否正常")
        
    def create_ec2_tab(self):
        """创建EC2标签页"""
        ec2_widget = QWidget()
        self.tab_widget.addTab(ec2_widget, "EC2")
        
        layout = QVBoxLayout(ec2_widget)
        
        # 顶部按钮区域
        top_layout = QHBoxLayout()
        
        # EC2按钮（橙色背景）
        ec2_btn = QPushButton("EC2")
        ec2_btn.setStyleSheet("QPushButton { background-color: #ff9900; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        ec2_btn.clicked.connect(self.refresh_ec2_instances)
        top_layout.addWidget(ec2_btn)
        
        # 保存为模板按钮
        save_template_btn = QPushButton("保存为模板")
        save_template_btn.clicked.connect(self.save_ec2_template)
        top_layout.addWidget(save_template_btn)
        
        top_layout.addStretch()
        layout.addLayout(top_layout)
        
        # 检查实例标题
        layout.addWidget(QLabel("检查实例"))
        
        # 区域选择
        region_layout = QHBoxLayout()
        region_layout.addWidget(QLabel("区域:"))
        self.ec2_region_combo = QComboBox()
        self.ec2_region_combo.addItems([
            "US East (N. Virginia) 美国东部",
            "US East (Ohio) 美国东部",
            "US West (Oregon) 美国西部",
            "US West (N. California) 美国西部",
            "Europe (Ireland) 欧洲",
            "Europe (London) 欧洲",
            "Europe (Frankfurt) 欧洲",
            "Asia Pacific (Singapore) 亚太地区",
            "Asia Pacific (Tokyo) 亚太地区",
            "Asia Pacific (Sydney) 亚太地区",
            "Asia Pacific (Mumbai) 亚太地区"
        ])
        region_layout.addWidget(self.ec2_region_combo)
        region_layout.addStretch()
        layout.addLayout(region_layout)
        
        # 测试按钮
        test_btn = QPushButton("测试")
        test_btn.setStyleSheet("QPushButton { background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        test_btn.clicked.connect(self.test_ec2_connection)
        layout.addWidget(test_btn)
        
        # 实例表格
        self.ec2_table = QTableWidget()
        self.ec2_table.setColumnCount(10)
        self.ec2_table.setHorizontalHeaderLabels([
            "名称", "实例", "状态", "实例类型", "公有IPv4", "私有IPv4", "网络IPv4", "监控", "状态", "平台"
        ])
        self.ec2_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.ec2_table)
        
        # 启动实例区域
        launch_group = QGroupBox("启动实例")
        launch_layout = QFormLayout(launch_group)
        
        # 实例配置
        self.ec2_name_edit = QLineEdit()
        self.ec2_name_edit.setPlaceholderText("实例名称")
        launch_layout.addRow("名称:", self.ec2_name_edit)
        
        self.ec2_instance_type_combo = QComboBox()
        self.ec2_instance_type_combo.addItems(["t2.micro", "t2.small", "t2.medium", "t3.micro", "t3.small"])
        launch_layout.addRow("实例类型:", self.ec2_instance_type_combo)
        
        self.ec2_ami_combo = QComboBox()
        self.ec2_ami_combo.addItems([
            "Ubuntu 24.04", "Ubuntu 22.04", "Ubuntu 20.04",
            "Debian 12", "Debian 11", "Debian 10",
            "Amazon Linux 2", "CentOS 7.9",
            "Red Hat Enterprise Linux 9", "Red Hat Enterprise Linux 8",
            "Windows Server 2022 简体中文版", "Windows Server 2022 English Version"
        ])
        launch_layout.addRow("AMI:", self.ec2_ami_combo)
        
        self.ec2_key_pair_edit = QLineEdit()
        self.ec2_key_pair_edit.setPlaceholderText("密钥对名称")
        launch_layout.addRow("密钥对:", self.ec2_key_pair_edit)
        
        # 启动按钮
        launch_ec2_btn = QPushButton("启动")
        launch_ec2_btn.setStyleSheet("QPushButton { background-color: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        launch_ec2_btn.clicked.connect(self.launch_ec2_instance)
        launch_layout.addRow("", launch_ec2_btn)
        
        layout.addWidget(launch_group)
        
    def create_lightsail_tab(self):
        """创建Lightsail标签页"""
        lightsail_widget = QWidget()
        self.tab_widget.addTab(lightsail_widget, "Lightsail")
        
        layout = QVBoxLayout(lightsail_widget)
        
        # 顶部按钮区域
        top_layout = QHBoxLayout()
        
        # Lightsail按钮（橙色背景）
        lightsail_btn = QPushButton("Lightsail")
        lightsail_btn.setStyleSheet("QPushButton { background-color: #ff9900; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        lightsail_btn.clicked.connect(self.refresh_lightsail_instances)
        top_layout.addWidget(lightsail_btn)
        
        # 一键开启实例按钮
        quick_start_btn = QPushButton("一键开启实例")
        quick_start_btn.setStyleSheet("QPushButton { background-color: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        quick_start_btn.clicked.connect(self.quick_start_lightsail)
        top_layout.addWidget(quick_start_btn)
        
        top_layout.addStretch()
        layout.addLayout(top_layout)
        
        # 测试配置标题
        layout.addWidget(QLabel("测试配置"))
        
        # 区域选择
        region_layout = QHBoxLayout()
        region_layout.addWidget(QLabel("区域:"))
        self.lightsail_region_combo = QComboBox()
        self.lightsail_region_combo.addItems([
            "US East (N. Virginia) 美国东部",
            "US East (Ohio) 美国东部",
            "US West (Oregon) 美国西部",
            "US West (N. California) 美国西部",
            "Europe (Ireland) 欧洲",
            "Europe (London) 欧洲",
            "Europe (Frankfurt) 欧洲",
            "Asia Pacific (Singapore) 亚太地区",
            "Asia Pacific (Tokyo) 亚太地区",
            "Asia Pacific (Sydney) 亚太地区",
            "Asia Pacific (Mumbai) 亚太地区"
        ])
        region_layout.addWidget(self.lightsail_region_combo)
        region_layout.addStretch()
        layout.addLayout(region_layout)
        
        # 测试按钮
        test_btn = QPushButton("测试")
        test_btn.setStyleSheet("QPushButton { background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        test_btn.clicked.connect(self.test_lightsail_connection)
        layout.addWidget(test_btn)
        
        # 启动实例区域
        launch_group = QGroupBox("启动实例")
        launch_layout = QFormLayout(launch_group)
        
        # 位置
        self.lightsail_location_combo = QComboBox()
        self.lightsail_location_combo.addItems([
            "US East (N. Virginia) 美国东部",
            "US East (Ohio) 美国东部",
            "US West (Oregon) 美国西部",
            "US West (N. California) 美国西部",
            "Europe (Ireland) 欧洲",
            "Europe (London) 欧洲",
            "Europe (Frankfurt) 欧洲",
            "Asia Pacific (Singapore) 亚太地区",
            "Asia Pacific (Tokyo) 亚太地区",
            "Asia Pacific (Sydney) 亚太地区",
            "Asia Pacific (Mumbai) 亚太地区"
        ])
        launch_layout.addRow("位置:", self.lightsail_location_combo)
        
        # 套餐
        self.lightsail_plan_combo = QComboBox()
        self.lightsail_plan_combo.addItems([
            "$3.50/月 (1 vCPU, 512 MB RAM)",
            "$5/月 (1 vCPU, 1 GB RAM)", 
            "$10/月 (1 vCPU, 2 GB RAM)",
            "$20/月 (2 vCPU, 4 GB RAM)"
        ])
        launch_layout.addRow("套餐:", self.lightsail_plan_combo)
        
        # 系统
        self.lightsail_os_combo = QComboBox()
        self.lightsail_os_combo.addItems([
            "Ubuntu 24.04", "Ubuntu 22.04", "Ubuntu 20.04",
            "Debian 12", "Debian 11", "Debian 10",
            "Amazon Linux 2", "CentOS 7.9",
            "Red Hat Enterprise Linux 9", "Red Hat Enterprise Linux 8",
            "Windows Server 2022 简体中文版", "Windows Server 2022 English Version"
        ])
        launch_layout.addRow("系统:", self.lightsail_os_combo)
        
        # 实例名称
        self.lightsail_name_edit = QLineEdit()
        self.lightsail_name_edit.setPlaceholderText("实例名称")
        launch_layout.addRow("实例名称:", self.lightsail_name_edit)
        
        # 启动按钮
        launch_lightsail_btn = QPushButton("启动")
        launch_lightsail_btn.setStyleSheet("QPushButton { background-color: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px; }")
        launch_lightsail_btn.clicked.connect(self.launch_lightsail_instance)
        launch_layout.addRow("", launch_lightsail_btn)
        
        layout.addWidget(launch_group)
        
    def get_region_code(self, region_text):
        """将区域显示文本转换为AWS区域代码"""
        region_map = {
            "US East (N. Virginia) 美国东部": "us-east-1",
            "US East (Ohio) 美国东部": "us-east-2",
            "US West (Oregon) 美国西部": "us-west-2",
            "US West (N. California) 美国西部": "us-west-1",
            "Europe (Ireland) 欧洲": "eu-west-1",
            "Europe (London) 欧洲": "eu-west-2",
            "Europe (Frankfurt) 欧洲": "eu-central-1",
            "Asia Pacific (Singapore) 亚太地区": "ap-southeast-1",
            "Asia Pacific (Tokyo) 亚太地区": "ap-northeast-1",
            "Asia Pacific (Sydney) 亚太地区": "ap-southeast-2",
            "Asia Pacific (Mumbai) 亚太地区": "ap-south-1"
        }
        return region_map.get(region_text, "us-east-1")
        
    def test_ec2_connection(self):
        """测试EC2连接"""
        # 首先检查凭证是否已配置
        if not self.ec2_client:
            QMessageBox.warning(self, "凭证未配置", "请先在上方配置AWS凭证，然后重试。")
            return

        try:
            region = self.get_region_code(self.ec2_region_combo.currentText())
            # 使用指定区域重新创建客户端
            self.ec2_client = boto3.client('ec2', region_name=region)

            # 测试连接
            response = self.ec2_client.describe_regions()
            QMessageBox.information(self, "连接成功", f"EC2连接测试成功！\n当前区域: {region}")

        except NoCredentialsError:
            QMessageBox.warning(self, "认证错误", "AWS凭证无效，请重新配置凭证。")
        except Exception as e:
            QMessageBox.critical(self, "连接失败", f"EC2连接失败: {str(e)}")

    def test_lightsail_connection(self):
        """测试Lightsail连接"""
        # 首先检查凭证是否已配置
        if not self.lightsail_client:
            QMessageBox.warning(self, "凭证未配置", "请先在上方配置AWS凭证，然后重试。")
            return

        try:
            region = self.get_region_code(self.lightsail_region_combo.currentText())
            # 使用指定区域重新创建客户端
            self.lightsail_client = boto3.client('lightsail', region_name=region)

            # 测试连接
            response = self.lightsail_client.get_regions()
            QMessageBox.information(self, "连接成功", f"Lightsail连接测试成功！\n当前区域: {region}")

        except NoCredentialsError:
            QMessageBox.warning(self, "认证错误", "AWS凭证无效，请重新配置凭证。")
        except Exception as e:
            QMessageBox.critical(self, "连接失败", f"Lightsail连接失败: {str(e)}")

    def refresh_ec2_instances(self):
        """刷新EC2实例列表"""
        if not self.ec2_client:
            QMessageBox.warning(self, "凭证未配置", "请先在上方配置AWS凭证，然后重试。")
            return

        try:
            response = self.ec2_client.describe_instances()

            # 清空表格
            self.ec2_table.setRowCount(0)

            # 填充实例数据
            row = 0
            for reservation in response['Reservations']:
                for instance in reservation['Instances']:
                    self.ec2_table.insertRow(row)

                    # 获取实例名称
                    name = ""
                    if 'Tags' in instance:
                        for tag in instance['Tags']:
                            if tag['Key'] == 'Name':
                                name = tag['Value']
                                break

                    self.ec2_table.setItem(row, 0, QTableWidgetItem(name))
                    self.ec2_table.setItem(row, 1, QTableWidgetItem(instance['InstanceId']))
                    self.ec2_table.setItem(row, 2, QTableWidgetItem(instance['State']['Name']))
                    self.ec2_table.setItem(row, 3, QTableWidgetItem(instance['InstanceType']))
                    self.ec2_table.setItem(row, 4, QTableWidgetItem(instance.get('PublicIpAddress', '')))
                    self.ec2_table.setItem(row, 5, QTableWidgetItem(instance.get('PrivateIpAddress', '')))
                    self.ec2_table.setItem(row, 6, QTableWidgetItem(instance.get('VpcId', '')))
                    self.ec2_table.setItem(row, 7, QTableWidgetItem('基本'))
                    self.ec2_table.setItem(row, 8, QTableWidgetItem(instance['State']['Name']))
                    self.ec2_table.setItem(row, 9, QTableWidgetItem(instance.get('Platform', 'Linux')))

                    row += 1

        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取EC2实例失败: {str(e)}")

    def refresh_lightsail_instances(self):
        """刷新Lightsail实例列表"""
        if not self.lightsail_client:
            self.test_lightsail_connection()
            if not self.lightsail_client:
                return

        try:
            response = self.lightsail_client.get_instances()
            print(f"Lightsail实例数量: {len(response.get('instances', []))}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取Lightsail实例失败: {str(e)}")

    def get_ami_id(self, ami_name, region):
        """根据AMI名称获取AMI ID - 通过搜索获取最新AMI"""
        try:
            filters = []

            if "Amazon Linux 2" in ami_name:
                filters = [
                    {'Name': 'name', 'Values': ['amzn2-ami-hvm-*']},
                    {'Name': 'owner-alias', 'Values': ['amazon']},
                    {'Name': 'state', 'Values': ['available']},
                    {'Name': 'architecture', 'Values': ['x86_64']}
                ]
            elif "Ubuntu 24.04" in ami_name:
                filters = [
                    {'Name': 'name', 'Values': ['ubuntu/images/hvm-ssd/ubuntu-noble-24.04-amd64-server-*']},
                    {'Name': 'owner-id', 'Values': ['099720109477']},
                    {'Name': 'state', 'Values': ['available']}
                ]
            elif "Ubuntu 22.04" in ami_name:
                filters = [
                    {'Name': 'name', 'Values': ['ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-*']},
                    {'Name': 'owner-id', 'Values': ['099720109477']},
                    {'Name': 'state', 'Values': ['available']}
                ]
            elif "Ubuntu 20.04" in ami_name:
                filters = [
                    {'Name': 'name', 'Values': ['ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-*']},
                    {'Name': 'owner-id', 'Values': ['099720109477']},
                    {'Name': 'state', 'Values': ['available']}
                ]
            elif "Debian 12" in ami_name:
                filters = [
                    {'Name': 'name', 'Values': ['debian-12-amd64-*']},
                    {'Name': 'owner-id', 'Values': ['136693071363']},
                    {'Name': 'state', 'Values': ['available']}
                ]
            elif "Debian 11" in ami_name:
                filters = [
                    {'Name': 'name', 'Values': ['debian-11-amd64-*']},
                    {'Name': 'owner-id', 'Values': ['136693071363']},
                    {'Name': 'state', 'Values': ['available']}
                ]
            elif "Debian 10" in ami_name:
                filters = [
                    {'Name': 'name', 'Values': ['debian-10-amd64-*']},
                    {'Name': 'owner-id', 'Values': ['136693071363']},
                    {'Name': 'state', 'Values': ['available']}
                ]
            elif "CentOS 7" in ami_name:
                filters = [
                    {'Name': 'name', 'Values': ['CentOS 7*']},
                    {'Name': 'owner-id', 'Values': ['125523088429']},
                    {'Name': 'state', 'Values': ['available']}
                ]
            elif "Red Hat Enterprise Linux 9" in ami_name:
                filters = [
                    {'Name': 'name', 'Values': ['RHEL-9*']},
                    {'Name': 'owner-id', 'Values': ['309956199498']},
                    {'Name': 'state', 'Values': ['available']}
                ]
            elif "Red Hat Enterprise Linux 8" in ami_name:
                filters = [
                    {'Name': 'name', 'Values': ['RHEL-8*']},
                    {'Name': 'owner-id', 'Values': ['309956199498']},
                    {'Name': 'state', 'Values': ['available']}
                ]
            elif "Windows Server 2022" in ami_name:
                if "简体中文版" in ami_name:
                    filters = [
                        {'Name': 'name', 'Values': ['Windows_Server-2022-Chinese_Simplified-Full-Base-*']},
                        {'Name': 'owner-alias', 'Values': ['amazon']},
                        {'Name': 'state', 'Values': ['available']}
                    ]
                else:
                    filters = [
                        {'Name': 'name', 'Values': ['Windows_Server-2022-English-Full-Base-*']},
                        {'Name': 'owner-alias', 'Values': ['amazon']},
                        {'Name': 'state', 'Values': ['available']}
                    ]
            else:
                return None

            response = self.ec2_client.describe_images(Filters=filters)
            if response['Images']:
                # 按创建日期排序，获取最新的
                images = sorted(response['Images'], key=lambda x: x['CreationDate'], reverse=True)
                return images[0]['ImageId']

        except Exception as e:
            print(f"搜索AMI失败: {e}")

        return None

    def launch_ec2_instance(self):
        """启动EC2实例"""
        if not self.ec2_client:
            QMessageBox.warning(self, "凭证未配置", "请先在上方配置AWS凭证，然后重试。")
            return

        try:
            # 获取配置
            name = self.ec2_name_edit.text().strip()
            instance_type = self.ec2_instance_type_combo.currentText()
            ami_name = self.ec2_ami_combo.currentText()
            key_pair = self.ec2_key_pair_edit.text().strip()
            region = self.get_region_code(self.ec2_region_combo.currentText())

            if not name:
                QMessageBox.warning(self, "输入错误", "请输入实例名称")
                return

            # 获取AMI ID
            ami_id = self.get_ami_id(ami_name, region)
            if not ami_id:
                QMessageBox.warning(self, "AMI错误", f"无法找到{ami_name}的AMI")
                return

            # 创建或获取安全组（全开放）
            security_group_id = self.create_open_security_group()

            # 启动实例参数
            launch_params = {
                'ImageId': ami_id,
                'MinCount': 1,
                'MaxCount': 1,
                'InstanceType': instance_type,
                'SecurityGroupIds': [security_group_id],
                'TagSpecifications': [
                    {
                        'ResourceType': 'instance',
                        'Tags': [
                            {'Key': 'Name', 'Value': name}
                        ]
                    }
                ]
            }

            # 如果指定了密钥对，添加到参数中
            if key_pair:
                launch_params['KeyName'] = key_pair

            # 启动实例
            response = self.ec2_client.run_instances(**launch_params)

            instance_id = response['Instances'][0]['InstanceId']
            QMessageBox.information(self, "成功", f"EC2实例创建成功！\n实例ID: {instance_id}")

            # 刷新实例列表
            self.refresh_ec2_instances()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建EC2实例失败: {str(e)}")

    def create_open_security_group(self):
        """创建全开放的安全组"""
        try:
            # 检查是否已存在全开放安全组
            response = self.ec2_client.describe_security_groups(
                Filters=[
                    {'Name': 'group-name', 'Values': ['open-all-sg']}
                ]
            )

            if response['SecurityGroups']:
                return response['SecurityGroups'][0]['GroupId']

            # 获取默认VPC
            vpc_response = self.ec2_client.describe_vpcs(
                Filters=[{'Name': 'is-default', 'Values': ['true']}]
            )

            if not vpc_response['Vpcs']:
                raise Exception("未找到默认VPC")

            vpc_id = vpc_response['Vpcs'][0]['VpcId']

            # 创建安全组
            sg_response = self.ec2_client.create_security_group(
                GroupName='open-all-sg',
                Description='Open all ports security group',
                VpcId=vpc_id
            )

            security_group_id = sg_response['GroupId']

            # 添加入站规则（全开放）
            self.ec2_client.authorize_security_group_ingress(
                GroupId=security_group_id,
                IpPermissions=[
                    {
                        'IpProtocol': '-1',
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    }
                ]
            )

            # 添加出站规则（全开放）
            self.ec2_client.authorize_security_group_egress(
                GroupId=security_group_id,
                IpPermissions=[
                    {
                        'IpProtocol': '-1',
                        'IpRanges': [{'CidrIp': '0.0.0.0/0'}]
                    }
                ]
            )

            return security_group_id

        except Exception as e:
            print(f"创建安全组失败: {e}")
            # 如果创建失败，尝试使用默认安全组
            try:
                response = self.ec2_client.describe_security_groups(
                    Filters=[{'Name': 'group-name', 'Values': ['default']}]
                )
                if response['SecurityGroups']:
                    return response['SecurityGroups'][0]['GroupId']
            except:
                pass
            raise e

    def get_lightsail_blueprint(self, os_name):
        """获取Lightsail蓝图ID"""
        blueprint_map = {
            "Ubuntu 24.04": "ubuntu_24_04",
            "Ubuntu 22.04": "ubuntu_22_04",
            "Ubuntu 20.04": "ubuntu_20_04",
            "Debian 12": "debian_12",
            "Debian 11": "debian_11",
            "Debian 10": "debian_10",
            "Amazon Linux 2": "amazon_linux_2",
            "CentOS 7.9": "centos_7_2009_01",
            "Red Hat Enterprise Linux 9": "rhel_9",
            "Red Hat Enterprise Linux 8": "rhel_8",
            "Windows Server 2022 简体中文版": "windows_server_2022",
            "Windows Server 2022 English Version": "windows_server_2022"
        }
        return blueprint_map.get(os_name, "ubuntu_24_04")

    def get_lightsail_bundle(self, plan_text):
        """获取Lightsail套餐ID"""
        bundle_map = {
            "$3.50/月 (1 vCPU, 512 MB RAM)": "nano_2_0",
            "$5/月 (1 vCPU, 1 GB RAM)": "micro_2_0",
            "$10/月 (1 vCPU, 2 GB RAM)": "small_2_0",
            "$20/月 (2 vCPU, 4 GB RAM)": "medium_2_0"
        }
        return bundle_map.get(plan_text, "micro_2_0")

    def launch_lightsail_instance(self):
        """启动Lightsail实例"""
        if not self.lightsail_client:
            QMessageBox.warning(self, "凭证未配置", "请先在上方配置AWS凭证，然后重试。")
            return

        try:
            # 获取配置
            name = self.lightsail_name_edit.text().strip()
            location = self.get_region_code(self.lightsail_location_combo.currentText())
            plan = self.get_lightsail_bundle(self.lightsail_plan_combo.currentText())
            os_name = self.lightsail_os_combo.currentText()
            blueprint = self.get_lightsail_blueprint(os_name)

            if not name:
                QMessageBox.warning(self, "输入错误", "请输入实例名称")
                return

            # 创建实例
            response = self.lightsail_client.create_instances(
                instanceNames=[name],
                availabilityZone=f"{location}a",  # 使用第一个可用区
                blueprintId=blueprint,
                bundleId=plan,
                tags=[
                    {'key': 'CreatedBy', 'value': 'AWS-Instance-Creator'}
                ]
            )

            QMessageBox.information(self, "成功", f"Lightsail实例创建成功！\n实例名称: {name}")

            # 创建全开放防火墙规则
            self.create_lightsail_open_firewall(name)

            # 刷新实例列表
            self.refresh_lightsail_instances()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建Lightsail实例失败: {str(e)}")

    def create_lightsail_open_firewall(self, instance_name):
        """为Lightsail实例创建全开放防火墙规则"""
        try:
            # 等待实例启动
            import time
            time.sleep(5)

            # 开放所有端口
            self.lightsail_client.open_instance_public_ports(
                instanceName=instance_name,
                portInfo={
                    'fromPort': 0,
                    'toPort': 65535,
                    'protocol': 'tcp'
                }
            )

            self.lightsail_client.open_instance_public_ports(
                instanceName=instance_name,
                portInfo={
                    'fromPort': 0,
                    'toPort': 65535,
                    'protocol': 'udp'
                }
            )

        except Exception as e:
            print(f"设置防火墙规则失败: {e}")

    def quick_start_lightsail(self):
        """一键启动Lightsail实例"""
        # 设置默认值
        self.lightsail_name_edit.setText("quick-instance")
        self.lightsail_plan_combo.setCurrentText("$5/月 (1 vCPU, 1 GB RAM)")
        self.lightsail_os_combo.setCurrentText("Ubuntu 24.04")

        # 启动实例
        self.launch_lightsail_instance()

    def save_ec2_template(self):
        """保存EC2配置为模板"""
        try:
            template = {
                "name": self.ec2_name_edit.text(),
                "instance_type": self.ec2_instance_type_combo.currentText(),
                "ami": self.ec2_ami_combo.currentText(),
                "key_pair": self.ec2_key_pair_edit.text(),
                "region": self.ec2_region_combo.currentText()
            }

            with open("ec2_template.json", "w", encoding="utf-8") as f:
                json.dump(template, f, ensure_ascii=False, indent=2)

            QMessageBox.information(self, "成功", "EC2模板已保存到 ec2_template.json")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存模板失败: {str(e)}")


def main():
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    window = AWSInstanceCreator()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
