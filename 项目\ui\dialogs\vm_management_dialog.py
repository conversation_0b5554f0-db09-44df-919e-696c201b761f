#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟机管理对话框
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QLabel, QMessageBox, QTextEdit, QSplitter)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont


class VMManagementDialog(QDialog):
    """虚拟机管理对话框"""
    
    def __init__(self, vm_widget, parent=None):
        super().__init__(parent)
        self.vm_widget = vm_widget
        self.init_ui()
        # 延迟加载数据，避免阻塞UI
        from PyQt6.QtCore import QTimer
        QTimer.singleShot(100, self.load_data)
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("虚拟机管理")
        self.setModal(True)
        self.resize(1000, 600)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("虚拟机列表")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_data)
        button_layout.addWidget(self.refresh_btn)

        self.view_details_btn = QPushButton("查看详情")
        self.view_details_btn.setEnabled(False)
        self.view_details_btn.clicked.connect(self.view_vm_details)
        button_layout.addWidget(self.view_details_btn)

        self.start_vm_btn = QPushButton("启动虚拟机")
        self.start_vm_btn.setEnabled(False)
        button_layout.addWidget(self.start_vm_btn)

        self.stop_vm_btn = QPushButton("停止虚拟机")
        self.stop_vm_btn.setEnabled(False)
        button_layout.addWidget(self.stop_vm_btn)

        self.restart_vm_btn = QPushButton("重启虚拟机")
        self.restart_vm_btn.setEnabled(False)
        button_layout.addWidget(self.restart_vm_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 虚拟机表格
        self.vm_table = QTableWidget()
        self.vm_table.setColumnCount(7)
        self.vm_table.setHorizontalHeaderLabels([
            "名称", "资源组", "位置", "规格", "状态", "公共IP", "私有IP"
        ])

        # 设置表格属性
        header = self.vm_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.vm_table.setAlternatingRowColors(True)
        self.vm_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.vm_table.itemSelectionChanged.connect(self.on_selection_changed)

        splitter.addWidget(self.vm_table)

        # 详情显示区域
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setPlainText("请选择虚拟机查看详细信息")
        self.details_text.setMaximumWidth(400)

        splitter.addWidget(self.details_text)
        splitter.setSizes([600, 400])

        layout.addWidget(splitter)
        
        # 关闭按钮
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        close_layout.addWidget(close_btn)
        
        layout.addLayout(close_layout)
        
    def load_data(self):
        """加载数据"""
        if hasattr(self.vm_widget, 'vms_data'):
            self.update_vm_table(self.vm_widget.vms_data)
        else:
            self.refresh_data()
            
    def refresh_data(self):
        """刷新数据"""
        self.vm_widget.refresh_data()
        # 连接信号以更新表格
        self.vm_widget.worker.data_loaded.connect(self.on_data_loaded)
        
    def on_data_loaded(self, data):
        """数据加载完成"""
        if data and len(data) > 0:
            result = data[0]
            vms = result.get('vms', [])
            self.update_vm_table(vms)
            
    def update_vm_table(self, vms_data):
        """更新虚拟机表格"""
        self.vm_table.setRowCount(len(vms_data))

        for row, vm in enumerate(vms_data):
            self.vm_table.setItem(row, 0, QTableWidgetItem(vm.get('name', '')))
            self.vm_table.setItem(row, 1, QTableWidgetItem(vm.get('resourceGroup', '')))
            self.vm_table.setItem(row, 2, QTableWidgetItem(vm.get('location', '')))
            self.vm_table.setItem(row, 3, QTableWidgetItem(vm.get('vmSize', '')))

            # 状态显示
            power_state = vm.get('powerState', '')
            status_item = QTableWidgetItem(power_state)
            if 'running' in power_state.lower():
                status_item.setBackground(Qt.GlobalColor.green)
            elif 'stopped' in power_state.lower() or 'deallocated' in power_state.lower():
                status_item.setBackground(Qt.GlobalColor.red)
            else:
                status_item.setBackground(Qt.GlobalColor.yellow)
            self.vm_table.setItem(row, 4, status_item)

            # 获取IP地址信息（需要从详细信息中获取）
            public_ip = vm.get('public_ip', '无')
            private_ip = vm.get('private_ip', '无')

            self.vm_table.setItem(row, 5, QTableWidgetItem(public_ip))
            self.vm_table.setItem(row, 6, QTableWidgetItem(private_ip))
            
    def on_selection_changed(self):
        """选择变化"""
        selected_rows = self.vm_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        self.view_details_btn.setEnabled(has_selection)
        self.start_vm_btn.setEnabled(has_selection)
        self.stop_vm_btn.setEnabled(has_selection)
        self.restart_vm_btn.setEnabled(has_selection)

        if has_selection:
            self.view_vm_details()
        else:
            self.details_text.setPlainText("请选择虚拟机查看详细信息")

    def view_vm_details(self):
        """查看虚拟机详情"""
        selected_rows = self.vm_table.selectionModel().selectedRows()
        if not selected_rows:
            return

        row = selected_rows[0].row()
        vm_name = self.vm_table.item(row, 0).text()
        resource_group = self.vm_table.item(row, 1).text()

        # 显示加载状态
        self.details_text.setPlainText("正在加载虚拟机详细信息...")

        # 获取详细信息
        try:
            if hasattr(self.vm_widget, 'auth_info') and self.vm_widget.auth_info:
                auth_client = self.vm_widget.auth_info.get('auth_client')
                if auth_client:
                    # 获取资源组详细信息，其中包含虚拟机的IP信息
                    details_data = auth_client.get_resource_group_details(resource_group)
                    self.display_vm_details(vm_name, details_data)
                else:
                    self.details_text.setPlainText("错误：认证客户端不可用")
            else:
                self.details_text.setPlainText("错误：未找到认证信息")
        except Exception as e:
            self.details_text.setPlainText(f"获取详细信息失败: {str(e)}")

    def display_vm_details(self, vm_name, details_data):
        """显示虚拟机详细信息"""
        if not details_data:
            self.details_text.setPlainText("无法获取虚拟机详细信息")
            return

        # 查找指定的虚拟机
        vm_info = None
        for vm in details_data.get('virtual_machines', []):
            if vm['name'] == vm_name:
                vm_info = vm
                break

        if not vm_info:
            self.details_text.setPlainText(f"未找到虚拟机 {vm_name} 的详细信息")
            return

        # 构建详细信息文本
        details_text = f"""虚拟机详细信息
{'='*30}

基本信息:
  名称: {vm_info['name']}
  规格: {vm_info['size']}
  位置: {vm_info['location']}

网络信息:"""

        if vm_info['public_ips']:
            details_text += f"\n  公共IP: {', '.join(vm_info['public_ips'])}"
        else:
            details_text += f"\n  公共IP: 无"

        if vm_info['private_ips']:
            details_text += f"\n  私有IP: {', '.join(vm_info['private_ips'])}"
        else:
            details_text += f"\n  私有IP: 无"

        # 获取当前行的其他信息
        row = self.vm_table.selectionModel().selectedRows()[0].row()
        power_state = self.vm_table.item(row, 4).text()

        details_text += f"""

状态信息:
  电源状态: {power_state}

操作提示:
  • 使用上方按钮可以启动、停止或重启虚拟机
  • 公共IP可用于远程连接
  • 私有IP用于内网通信"""

        self.details_text.setPlainText(details_text)
