#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证组件
"""

import json
import webbrowser
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QTextEdit, QPushButton, QGroupBox,
                             QMessageBox, QComboBox, QSizePolicy, QTabWidget)
from PyQt6.QtCore import QThread, pyqtSignal, QDateTime
from PyQt6.QtGui import QFont

from azure_client.auth_client import AuthClient, AzureCliAuthClient


class AuthWorker(QThread):
    """认证工作线程"""
    auth_success = pyqtSignal(dict)
    auth_failed = pyqtSignal(str)
    progress_update = pyqtSignal(int, str)
    
    def __init__(self, auth_info):
        super().__init__()
        self.auth_info = auth_info
        
    def run(self):
        """执行认证"""
        try:
            self.progress_update.emit(20, "正在验证认证信息...")

            # 创建认证客户端
            auth_client = AuthClient(self.auth_info)

            self.progress_update.emit(40, "正在连接Azure...")

            # 验证认证
            if auth_client.authenticate():
                self.progress_update.emit(60, "正在获取订阅信息...")

                # 获取订阅信息（可能失败，但不影响整体认证）
                subscription_info = auth_client.get_subscription_info()
                if not subscription_info:
                    subscription_info = {
                        'displayName': 'Unknown Subscription',
                        'subscriptionId': self.auth_info.get('subscription_id', 'Unknown'),
                        'state': 'Unknown',
                        'tenantId': self.auth_info.get('tenant', 'Unknown')
                    }

                self.progress_update.emit(80, "正在获取位置信息...")

                # 获取可用位置（可能失败，使用默认位置）
                locations = auth_client.get_locations()
                if not locations:
                    locations = [
                        {'name': 'eastus', 'displayName': 'East US', 'regionalDisplayName': 'East US'},
                        {'name': 'westus', 'displayName': 'West US', 'regionalDisplayName': 'West US'},
                        {'name': 'centralus', 'displayName': 'Central US', 'regionalDisplayName': 'Central US'}
                    ]

                self.progress_update.emit(100, "认证完成")

                result = {
                    'subscription_info': subscription_info,
                    'locations': locations,
                    'auth_client': auth_client
                }

                self.auth_success.emit(result)
            else:
                self.auth_failed.emit("认证失败，请检查认证信息")

        except Exception as e:
            self.auth_failed.emit(f"认证过程中发生错误: {str(e)}")


class AzureCliAuthWorker(QThread):
    """Azure CLI认证工作线程"""
    auth_success = pyqtSignal(dict)
    auth_failed = pyqtSignal(str)
    progress_update = pyqtSignal(int, str)
    device_code_ready = pyqtSignal(dict)

    def __init__(self, subscription_id=None):
        super().__init__()
        self.subscription_id = subscription_id

    def run(self):
        """执行Azure CLI认证"""
        try:
            self.progress_update.emit(10, "正在检查Azure CLI登录状态...")

            # 创建Azure CLI认证客户端
            cli_client = AzureCliAuthClient(self.subscription_id)

            # 检查当前登录状态
            login_status = cli_client.check_login_status()

            if login_status.get('success') and login_status.get('logged_in'):
                self.progress_update.emit(30, "已登录Azure CLI，正在验证认证...")

                # 已经登录，直接进行认证
                if cli_client.authenticate():
                    self.progress_update.emit(60, "正在获取订阅信息...")

                    subscription_info = cli_client.get_subscription_info()
                    if not subscription_info:
                        subscription_info = {
                            'displayName': 'Unknown Subscription',
                            'subscriptionId': self.subscription_id or 'Unknown',
                            'state': 'Unknown',
                            'tenantId': 'Unknown'
                        }

                    self.progress_update.emit(80, "正在获取位置信息...")
                    locations = cli_client.get_locations()

                    self.progress_update.emit(100, "认证完成")

                    result = {
                        'subscription_info': subscription_info,
                        'locations': locations,
                        'auth_client': cli_client,
                        'subscriptions': cli_client.get_subscriptions()
                    }

                    self.auth_success.emit(result)
                else:
                    self.auth_failed.emit("Azure CLI认证失败")
            else:
                # 需要重新登录
                self.progress_update.emit(20, "启动设备代码流认证...")

                device_flow_result = cli_client.start_device_code_flow()

                if device_flow_result.get('success'):
                    # 发送设备代码信息
                    self.device_code_ready.emit(device_flow_result)

                    # 等待用户完成认证（轮询检查）
                    self.progress_update.emit(30, "等待用户完成浏览器认证...")

                    max_attempts = 60  # 最多等待5分钟
                    attempt = 0

                    while attempt < max_attempts:
                        self.msleep(5000)  # 等待5秒
                        attempt += 1

                        # 检查登录状态
                        status = cli_client.check_login_status()
                        if status.get('success') and status.get('logged_in'):
                            self.progress_update.emit(60, "认证成功，正在获取信息...")

                            # 进行认证
                            if cli_client.authenticate():
                                subscription_info = cli_client.get_subscription_info()
                                if not subscription_info:
                                    subscription_info = {
                                        'displayName': 'Unknown Subscription',
                                        'subscriptionId': self.subscription_id or 'Unknown',
                                        'state': 'Unknown',
                                        'tenantId': 'Unknown'
                                    }

                                self.progress_update.emit(80, "正在获取位置信息...")
                                locations = cli_client.get_locations()

                                self.progress_update.emit(100, "认证完成")

                                result = {
                                    'subscription_info': subscription_info,
                                    'locations': locations,
                                    'auth_client': cli_client,
                                    'subscriptions': cli_client.get_subscriptions()
                                }

                                self.auth_success.emit(result)
                                return
                            else:
                                self.auth_failed.emit("Azure CLI认证失败")
                                return

                        # 更新进度
                        progress = 30 + (attempt * 30 // max_attempts)
                        self.progress_update.emit(progress, f"等待用户完成认证... ({attempt}/{max_attempts})")

                    # 超时
                    self.auth_failed.emit("认证超时，请重试")
                else:
                    self.auth_failed.emit(device_flow_result.get('error', '启动设备代码流失败'))

        except Exception as e:
            self.auth_failed.emit(f"Azure CLI认证过程中发生错误: {str(e)}")


class DeviceCodeWorker(QThread):
    """设备代码获取工作线程"""
    device_code_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def run(self):
        """获取设备代码"""
        try:
            from azure_client.auth_client import AzureCliAuthClient

            # 检查Azure CLI是否已安装
            cli_check = AzureCliAuthClient.check_azure_cli_installed()

            if not cli_check.get('installed', False):
                self.error_occurred.emit(f"Azure CLI未安装: {cli_check.get('error', '未知错误')}")
                return

            # 执行设备代码流认证
            cli_client = AzureCliAuthClient()
            device_flow_result = cli_client.start_device_code_flow()

            if device_flow_result.get('success'):
                self.device_code_ready.emit(device_flow_result)
            else:
                self.error_occurred.emit(f"启动设备代码流失败: {device_flow_result.get('error', '未知错误')}")

        except Exception as e:
            self.error_occurred.emit(f"获取设备代码失败: {str(e)}")


class RefreshAuthWorker(QThread):
    """刷新认证状态工作线程"""
    auth_success = pyqtSignal(dict)
    auth_failed = pyqtSignal(str)
    status_update = pyqtSignal(str)

    def run(self):
        """刷新认证状态"""
        try:
            from azure_client.auth_client import AzureCliAuthClient

            self.status_update.emit("正在检查Azure CLI...")

            # 检查Azure CLI是否已安装
            cli_check = AzureCliAuthClient.check_azure_cli_installed()

            if not cli_check.get('installed', False):
                self.auth_failed.emit(f"Azure CLI未安装: {cli_check.get('error', '未知错误')}")
                return

            self.status_update.emit("正在检查登录状态...")

            # 检查登录状态
            cli_client = AzureCliAuthClient()
            status = cli_client.check_login_status()

            if status.get('success'):
                if status.get('logged_in'):
                    self.status_update.emit("正在进行认证...")

                    # 已登录，尝试进行完整认证
                    account_info = status.get('account_info', {})

                    # 执行认证流程
                    if cli_client.authenticate():
                        self.status_update.emit("正在获取订阅信息...")

                        # 获取订阅信息
                        subscription_info = cli_client.get_subscription_info()
                        if not subscription_info:
                            subscription_info = {
                                'displayName': account_info.get('name', 'Unknown'),
                                'subscriptionId': account_info.get('id', 'Unknown'),
                                'state': 'Enabled',
                                'tenantId': account_info.get('tenantId', 'Unknown')
                            }

                        self.status_update.emit("正在获取位置信息...")

                        # 获取位置信息
                        locations = cli_client.get_locations()

                        # 获取所有订阅
                        subscriptions = cli_client.get_subscriptions()

                        # 构建认证结果
                        result = {
                            'subscription_info': subscription_info,
                            'locations': locations,
                            'auth_client': cli_client,
                            'subscriptions': subscriptions,
                            'account_info': account_info
                        }

                        self.auth_success.emit(result)
                    else:
                        self.auth_failed.emit("Azure CLI已登录但认证失败，请重新登录")
                else:
                    self.auth_failed.emit("未登录Azure CLI，请点击'开始Azure CLI认证'进行登录")
            else:
                self.auth_failed.emit(f"检查登录状态失败: {status.get('error', '未知错误')}")

        except Exception as e:
            self.auth_failed.emit(f"刷新认证状态失败: {str(e)}")


class VerifyAuthWorker(QThread):
    """验证认证状态工作线程"""
    auth_verified = pyqtSignal(dict)
    auth_failed = pyqtSignal(str)

    def __init__(self, subscription_id):
        super().__init__()
        self.subscription_id = subscription_id

    def run(self):
        """验证认证状态"""
        try:
            from azure_client.auth_client import AzureCliAuthClient

            # 检查Azure CLI是否已安装
            cli_check = AzureCliAuthClient.check_azure_cli_installed()

            if not cli_check.get('installed', False):
                self.auth_failed.emit(f"Azure CLI未安装: {cli_check.get('error', '未知错误')}")
                return

            # 检查登录状态
            cli_client = AzureCliAuthClient(self.subscription_id)
            status = cli_client.check_login_status()

            if status.get('success') and status.get('logged_in'):
                # 尝试认证
                if cli_client.authenticate():
                    # 获取位置信息
                    locations = cli_client.get_locations()

                    # 构建认证结果
                    result = {
                        'auth_client': cli_client,
                        'locations': locations
                    }

                    self.auth_verified.emit(result)
                else:
                    self.auth_failed.emit("Azure CLI已登录但认证失败")
            else:
                self.auth_failed.emit("Azure CLI未登录或登录状态无效")

        except Exception as e:
            self.auth_failed.emit(f"验证认证状态失败: {str(e)}")


class AuthWidget(QWidget):
    """认证组件"""
    
    auth_success = pyqtSignal(dict)
    auth_failed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.auth_client = None
        self.subscription_info = None
        self.locations = []
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # 认证组
        auth_group = QGroupBox("Azure认证")
        auth_layout = QVBoxLayout(auth_group)

        # 创建选项卡
        self.tab_widget = QTabWidget()

        # 服务主体认证选项卡
        self.service_principal_tab = QWidget()
        self.init_service_principal_tab()
        self.tab_widget.addTab(self.service_principal_tab, "服务主体认证")

        # Azure CLI认证选项卡
        self.azure_cli_tab = QWidget()
        self.init_azure_cli_tab()
        self.tab_widget.addTab(self.azure_cli_tab, "Azure CLI认证")

        auth_layout.addWidget(self.tab_widget)

        # 状态区域
        self.status_label = QLabel("未认证")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")
        auth_layout.addWidget(self.status_label)

        # 订阅信息区域
        info_layout = QHBoxLayout()

        sub_label = QLabel("订阅:")
        info_layout.addWidget(sub_label)

        self.subscription_combo = QComboBox()
        self.subscription_combo.setEnabled(False)
        self.subscription_combo.setMinimumWidth(400)
        self.subscription_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        info_layout.addWidget(self.subscription_combo)

        # 订阅管理按钮
        self.save_subscription_btn = QPushButton("保存订阅")
        self.save_subscription_btn.setEnabled(False)
        self.save_subscription_btn.clicked.connect(self.save_subscription)
        info_layout.addWidget(self.save_subscription_btn)

        self.clear_subscription_btn = QPushButton("清除订阅")
        self.clear_subscription_btn.clicked.connect(self.clear_subscription)
        info_layout.addWidget(self.clear_subscription_btn)

        info_layout.addStretch()
        auth_layout.addLayout(info_layout)

        layout.addWidget(auth_group)

        # 尝试加载保存的订阅信息
        self.load_saved_subscription()

    def init_service_principal_tab(self):
        """初始化服务主体认证选项卡"""
        layout = QVBoxLayout(self.service_principal_tab)

        # JSON输入区域
        json_label = QLabel("粘贴认证JSON:")
        json_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(json_label)

        self.json_text = QTextEdit()
        self.json_text.setMaximumHeight(120)
        self.json_text.setPlaceholderText("""请粘贴Azure认证JSON，格式如下:
{
    "subscription_id": "your-subscription-id",
    "appId": "your-app-id",
    "password": "your-password",
    "tenant": "your-tenant-id"
}""")
        layout.addWidget(self.json_text)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.auth_btn = QPushButton("刷新认证")
        self.auth_btn.clicked.connect(self.authenticate_service_principal)
        button_layout.addWidget(self.auth_btn)

        self.clear_btn = QPushButton("清除")
        self.clear_btn.clicked.connect(self.clear_json)
        button_layout.addWidget(self.clear_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

    def init_azure_cli_tab(self):
        """初始化Azure CLI认证选项卡"""
        layout = QVBoxLayout(self.azure_cli_tab)

        # 说明文本
        info_label = QLabel("Azure CLI设备代码认证")
        info_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(info_label)

        desc_label = QLabel("""使用Azure CLI进行认证：
1. 点击"开始Azure CLI认证"按钮
2. 系统会生成设备代码和验证URL
3. 在浏览器中打开验证URL并输入设备代码
4. 完成认证后系统会自动检测并完成登录""")
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #666; margin: 10px 0;")
        layout.addWidget(desc_label)

        # 提示信息
        tip_label = QLabel("认证完成后，请点击'刷新认证状态'按钮")
        tip_label.setStyleSheet("color: #666; font-style: italic; margin: 10px 0;")
        layout.addWidget(tip_label)

        # Azure CLI认证按钮
        cli_button_layout = QHBoxLayout()

        self.cli_auth_btn = QPushButton("开始Azure CLI认证")
        self.cli_auth_btn.clicked.connect(self.authenticate_azure_cli)
        cli_button_layout.addWidget(self.cli_auth_btn)

        self.refresh_status_btn = QPushButton("刷新认证状态")
        self.refresh_status_btn.clicked.connect(self.refresh_auth_status)
        cli_button_layout.addWidget(self.refresh_status_btn)

        cli_button_layout.addStretch()
        layout.addLayout(cli_button_layout)

        layout.addStretch()

    def authenticate_service_principal(self):
        """执行服务主体认证"""
        json_text = self.json_text.toPlainText().strip()

        if not json_text:
            QMessageBox.warning(self, "警告", "请先粘贴认证JSON")
            return

        try:
            # 解析JSON
            auth_info = json.loads(json_text)

            # 验证必要字段
            required_fields = ['subscription_id', 'appId', 'password', 'tenant']
            for field in required_fields:
                if field not in auth_info:
                    QMessageBox.warning(self, "错误", f"JSON中缺少必要字段: {field}")
                    return

            # 更新状态
            self.auth_btn.setEnabled(False)
            self.status_label.setText("正在认证...")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")

            # 启动认证线程
            self.auth_worker = AuthWorker(auth_info)
            self.auth_worker.auth_success.connect(self.on_auth_success)
            self.auth_worker.auth_failed.connect(self.on_auth_failed)
            self.auth_worker.progress_update.connect(self.on_progress_update)
            self.auth_worker.start()

        except json.JSONDecodeError:
            QMessageBox.warning(self, "错误", "JSON格式错误，请检查格式")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"认证过程中发生错误: {str(e)}")

    def authenticate_azure_cli(self):
        """执行Azure CLI认证"""
        # 禁用按钮，防止重复点击
        self.cli_auth_btn.setEnabled(False)
        self.cli_auth_btn.setText("正在启动...")
        self.status_label.setText("正在启动Azure CLI认证...")
        self.status_label.setStyleSheet("color: orange; font-weight: bold;")

        # 创建并启动后台线程
        self.device_code_worker = DeviceCodeWorker()
        self.device_code_worker.device_code_ready.connect(self.on_device_code_ready)
        self.device_code_worker.error_occurred.connect(self.on_device_code_error)
        self.device_code_worker.start()

    def on_device_code_ready(self, device_info):
        """设备代码准备就绪"""
        # 恢复按钮状态
        self.cli_auth_btn.setEnabled(True)
        self.cli_auth_btn.setText("开始Azure CLI认证")
        self.status_label.setText("请完成浏览器认证")
        self.status_label.setStyleSheet("color: blue; font-weight: bold;")

        # 显示设备代码弹窗
        self.show_device_code_dialog(device_info)

    def on_device_code_error(self, error_msg):
        """设备代码获取失败"""
        # 恢复按钮状态
        self.cli_auth_btn.setEnabled(True)
        self.cli_auth_btn.setText("开始Azure CLI认证")
        self.status_label.setText("认证失败")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")

        QMessageBox.critical(self, "认证失败", error_msg)

    def show_device_code_dialog(self, device_info):
        """显示设备代码弹窗"""
        device_code = device_info.get('device_code')
        verification_url = device_info.get('verification_url')

        # 创建自定义消息框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("Azure CLI 设备代码认证")
        msg_box.setIcon(QMessageBox.Icon.Information)

        # 设置主要文本
        msg_box.setText("请在浏览器中完成认证")

        # 设置详细文本
        detail_text = f"""设备代码认证步骤：

1. 复制下面的设备代码：
   {device_code}

2. 打开浏览器访问：
   {verification_url}

3. 在网页中输入设备代码并登录您的Azure账户

4. 完成认证后，点击"刷新认证状态"按钮检查登录状态"""

        msg_box.setDetailedText(detail_text)

        # 添加按钮
        copy_btn = msg_box.addButton("复制设备代码", QMessageBox.ButtonRole.ActionRole)
        open_browser_btn = msg_box.addButton("打开浏览器", QMessageBox.ButtonRole.ActionRole)
        msg_box.addButton("确定", QMessageBox.ButtonRole.AcceptRole)

        # 显示对话框
        msg_box.exec()

        # 处理按钮点击
        clicked_button = msg_box.clickedButton()

        if clicked_button == copy_btn:
            # 复制设备代码到剪贴板
            try:
                from PyQt6.QtWidgets import QApplication
                clipboard = QApplication.clipboard()
                clipboard.setText(device_code)
                QMessageBox.information(self, "提示", f"设备代码已复制到剪贴板:\n{device_code}")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"复制失败: {str(e)}")

        elif clicked_button == open_browser_btn:
            # 打开浏览器
            try:
                webbrowser.open(verification_url)
            except Exception as e:
                QMessageBox.warning(self, "错误", f"打开浏览器失败: {str(e)}")

    def refresh_auth_status(self):
        """刷新Azure CLI认证状态"""
        # 显示正在刷新的状态
        self.refresh_status_btn.setText("刷新中...")
        self.refresh_status_btn.setEnabled(False)
        self.status_label.setText("正在刷新认证状态...")
        self.status_label.setStyleSheet("color: orange; font-weight: bold;")

        # 创建并启动刷新工作线程
        self.refresh_worker = RefreshAuthWorker()
        self.refresh_worker.auth_success.connect(self.on_refresh_success)
        self.refresh_worker.auth_failed.connect(self.on_refresh_failed)
        self.refresh_worker.status_update.connect(self.on_refresh_status_update)
        self.refresh_worker.start()

    def on_refresh_success(self, result):
        """刷新成功处理"""
        # 恢复按钮状态
        self.refresh_status_btn.setText("刷新认证状态")
        self.refresh_status_btn.setEnabled(True)

        # 触发认证成功事件
        self.on_auth_success(result)

        # 显示成功消息
        account_info = result.get('account_info', {})
        subscription_info = result.get('subscription_info', {})

        QMessageBox.information(
            self,
            "认证状态",
            f"✅ Azure CLI认证成功!\n\n"
            f"账户: {account_info.get('user', {}).get('name', 'Unknown')}\n"
            f"订阅: {subscription_info.get('displayName', 'Unknown')}\n"
            f"订阅ID: {subscription_info.get('subscriptionId', 'Unknown')}"
        )

    def on_refresh_failed(self, error_msg):
        """刷新失败处理"""
        # 恢复按钮状态
        self.refresh_status_btn.setText("刷新认证状态")
        self.refresh_status_btn.setEnabled(True)
        self.status_label.setText("认证失败")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")

        # 根据错误类型显示不同的消息
        if "未登录" in error_msg:
            QMessageBox.information(
                self,
                "认证状态",
                "❌ 未登录Azure CLI\n\n请点击'开始Azure CLI认证'进行登录"
            )
        elif "未安装" in error_msg:
            # 显示安装指导
            from azure_client.auth_client import AzureCliAuthClient
            cli_check = AzureCliAuthClient.check_azure_cli_installed()
            self.show_azure_cli_install_guide(cli_check)
        else:
            QMessageBox.critical(self, "错误", f"刷新认证状态失败:\n{error_msg}")

    def on_refresh_status_update(self, status_text):
        """刷新状态更新"""
        self.status_label.setText(status_text)
        self.status_label.setStyleSheet("color: orange; font-weight: bold;")

    def show_azure_cli_install_guide(self, cli_check):
        """显示Azure CLI安装指导"""
        error_msg = cli_check.get('error', '未知错误')
        install_guide = cli_check.get('install_guide', {})

        if install_guide:
            # 创建详细的安装指导消息
            guide_text = f"错误: {error_msg}\n\n"
            guide_text += "Azure CLI安装指导:\n\n"

            windows_guide = install_guide.get('windows', [])
            for line in windows_guide:
                guide_text += f"{line}\n"

            guide_text += "\n安装完成后，请重启应用程序并重试。"

            # 创建自定义消息框
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("Azure CLI未安装")
            msg_box.setIcon(QMessageBox.Icon.Information)
            msg_box.setText("需要安装Azure CLI才能使用此认证方式")
            msg_box.setDetailedText(guide_text)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Help)

            # 添加自定义按钮
            download_btn = msg_box.addButton("下载Azure CLI", QMessageBox.ButtonRole.ActionRole)

            result = msg_box.exec()

            # 处理按钮点击
            if msg_box.clickedButton() == download_btn:
                try:
                    webbrowser.open("https://aka.ms/installazurecliwindows")
                except Exception as e:
                    QMessageBox.warning(self, "错误", f"无法打开下载页面: {str(e)}")
        else:
            QMessageBox.critical(self, "Azure CLI错误", error_msg)

    def on_auth_success(self, result):
        """认证成功处理"""
        self.auth_client = result['auth_client']
        self.subscription_info = result['subscription_info']
        self.locations = result['locations']

        # 更新UI状态
        self.auth_btn.setEnabled(True)
        self.cli_auth_btn.setEnabled(True)

        # 检查订阅信息是否有效
        if self.subscription_info:
            display_name = self.subscription_info.get('displayName', 'Unknown')
            subscription_id = self.subscription_info.get('subscriptionId', 'Unknown')

            self.status_label.setText(f"已认证: {display_name}")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")

            # 更新订阅下拉框
            self.subscription_combo.clear()

            # 如果有多个订阅（Azure CLI认证可能返回多个订阅）
            subscriptions = result.get('subscriptions', [])
            if subscriptions and len(subscriptions) > 1:
                for sub in subscriptions:
                    sub_display_name = sub.get('displayName', 'Unknown')
                    sub_id = sub.get('subscriptionId', 'Unknown')
                    sub_state = sub.get('state', 'Unknown')

                    display_text = f"{sub_display_name} | {sub_id} | 状态: {sub_state}"
                    self.subscription_combo.addItem(display_text)

                    # 设置当前订阅为选中项
                    if sub_id == subscription_id:
                        self.subscription_combo.setCurrentIndex(self.subscription_combo.count() - 1)
            else:
                # 单个订阅或服务主体认证
                state = self.subscription_info.get('state', 'Unknown')
                display_text = f"{display_name} | {subscription_id} | 状态: {state}"
                self.subscription_combo.addItem(display_text)

            self.subscription_combo.setEnabled(True)

            # 启用保存订阅按钮
            self.save_subscription_btn.setEnabled(True)

            # 设置工具提示显示完整信息
            state = self.subscription_info.get('state', 'Unknown')
            tenant_id = self.subscription_info.get('tenantId', 'Unknown')
            tooltip_text = f"订阅名称: {display_name}\n订阅ID: {subscription_id}\n状态: {state}\n租户ID: {tenant_id}"
            self.subscription_combo.setToolTip(tooltip_text)

            # 发送成功信号
            self.auth_success.emit(self.subscription_info)
        else:
            self.status_label.setText("认证成功但无法获取订阅信息")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")

            # 创建默认订阅信息
            default_info = {'displayName': 'Unknown', 'subscriptionId': 'Unknown'}
            self.auth_success.emit(default_info)
        
    def on_auth_failed(self, error_msg):
        """认证失败处理"""
        # 重新启用按钮
        self.auth_btn.setEnabled(True)
        self.cli_auth_btn.setEnabled(True)

        self.status_label.setText("认证失败")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")

        QMessageBox.critical(self, "认证失败", error_msg)

        # 发送失败信号
        self.auth_failed.emit(error_msg)

    def save_subscription(self):
        """保存当前订阅信息到本地文件"""
        try:
            if not hasattr(self, 'subscription_info') or not self.subscription_info:
                QMessageBox.warning(self, "警告", "没有可保存的订阅信息，请先完成认证")
                return

            import json
            import os

            # 创建配置目录
            config_dir = os.path.expanduser("~/.azure_manager")
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            # 保存订阅信息
            config_file = os.path.join(config_dir, "subscription.json")

            # 准备保存的数据
            save_data = {
                'subscription_info': self.subscription_info,
                'auth_type': 'azure_cli' if hasattr(self, 'cli_auth_btn') else 'service_principal',
                'saved_time': str(QDateTime.currentDateTime().toString())
            }

            # 如果有多个订阅，也保存
            if hasattr(self, 'auth_client'):
                auth_info = self.get_auth_info()
                auth_client = auth_info.get('auth_client')
                if auth_client and hasattr(auth_client, 'get_subscriptions'):
                    try:
                        subscriptions = auth_client.get_subscriptions()
                        save_data['all_subscriptions'] = subscriptions
                    except:
                        pass

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            QMessageBox.information(
                self,
                "保存成功",
                f"订阅信息已保存到:\n{config_file}\n\n"
                f"订阅名称: {self.subscription_info.get('displayName', 'Unknown')}\n"
                f"订阅ID: {self.subscription_info.get('subscriptionId', 'Unknown')}"
            )

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存订阅信息失败:\n{str(e)}")

    def clear_subscription(self):
        """清除保存的订阅信息"""
        try:
            import os

            config_file = os.path.expanduser("~/.azure_manager/subscription.json")

            if os.path.exists(config_file):
                # 询问用户确认
                reply = QMessageBox.question(
                    self,
                    "确认清除",
                    "确定要清除保存的订阅信息吗？\n这将删除本地保存的配置文件。",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.Yes:
                    os.remove(config_file)
                    QMessageBox.information(self, "清除成功", "保存的订阅信息已清除")

                    # 重置UI状态
                    self.subscription_combo.clear()
                    self.subscription_combo.setEnabled(False)
                    self.save_subscription_btn.setEnabled(False)
                    self.status_label.setText("未认证")
                    self.status_label.setStyleSheet("color: red; font-weight: bold;")

                    # 清除内存中的认证信息
                    if hasattr(self, 'auth_client'):
                        self.auth_client = None
                    if hasattr(self, 'subscription_info'):
                        self.subscription_info = None
                    if hasattr(self, 'locations'):
                        self.locations = None
            else:
                QMessageBox.information(self, "提示", "没有找到保存的订阅信息")

        except Exception as e:
            QMessageBox.critical(self, "清除失败", f"清除订阅信息失败:\n{str(e)}")

    def load_saved_subscription(self):
        """加载保存的订阅信息并尝试重新认证"""
        try:
            import json
            import os

            config_file = os.path.expanduser("~/.azure_manager/subscription.json")

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_data = json.load(f)

                subscription_info = saved_data.get('subscription_info')
                auth_type = saved_data.get('auth_type', 'azure_cli')

                if subscription_info:
                    # 显示保存的订阅信息
                    display_name = subscription_info.get('displayName', 'Unknown')
                    subscription_id = subscription_info.get('subscriptionId', 'Unknown')
                    state = subscription_info.get('state', 'Unknown')

                    self.subscription_combo.clear()
                    display_text = f"{display_name} | {subscription_id} | 状态: {state} (已保存)"
                    self.subscription_combo.addItem(display_text)
                    self.subscription_combo.setEnabled(True)

                    # 保存订阅信息到实例变量
                    self.subscription_info = subscription_info

                    # 尝试重新认证以启用功能按钮
                    if auth_type == 'azure_cli':
                        self.status_label.setText("正在验证Azure CLI认证...")
                        self.status_label.setStyleSheet("color: orange; font-weight: bold;")

                        # 创建后台线程验证认证
                        self.verify_worker = VerifyAuthWorker(subscription_id)
                        self.verify_worker.auth_verified.connect(self.on_auth_verified)
                        self.verify_worker.auth_failed.connect(self.on_auth_verify_failed)
                        self.verify_worker.start()
                    else:
                        self.status_label.setText(f"已加载保存的订阅: {display_name} (需要重新认证)")
                        self.status_label.setStyleSheet("color: orange; font-weight: bold;")

                    return True

            return False

        except Exception as e:
            print(f"加载保存的订阅信息失败: {str(e)}")
            return False

    def on_auth_verified(self, auth_result):
        """认证验证成功"""
        self.auth_client = auth_result['auth_client']
        self.locations = auth_result.get('locations', [])

        # 启用保存订阅按钮
        self.save_subscription_btn.setEnabled(True)

        # 更新状态
        if self.subscription_info:
            display_name = self.subscription_info.get('displayName', 'Unknown')
            self.status_label.setText(f"✅ 已验证订阅: {display_name}")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")

            # 发送认证成功信号，启用功能按钮
            self.auth_success.emit(self.subscription_info)
        else:
            self.status_label.setText("✅ 认证验证成功")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")

            # 创建一个基本的订阅信息
            basic_subscription_info = {
                'displayName': 'Azure CLI认证',
                'subscriptionId': 'Unknown',
                'state': 'Enabled'
            }
            self.auth_success.emit(basic_subscription_info)

    def on_auth_verify_failed(self, error_msg):
        """认证验证失败"""
        if self.subscription_info:
            display_name = self.subscription_info.get('displayName', 'Unknown')
            self.status_label.setText(f"❌ 订阅已加载但认证失效: {display_name}")
        else:
            self.status_label.setText("❌ 认证验证失败")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")

        # 显示提示信息
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.warning(
            self,
            "认证验证失败",
            f"认证验证失败。\n\n"
            f"错误: {error_msg}\n\n"
            f"请重新进行Azure CLI认证以启用管理功能。"
        )

    def clear_json(self):
        """清除JSON输入"""
        self.json_text.clear()

    def get_auth_info(self):
        """获取认证信息"""
        return {
            'auth_client': self.auth_client,
            'subscription_info': self.subscription_info,
            'locations': self.locations
        }
