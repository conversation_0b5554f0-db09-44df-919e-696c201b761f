#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具类
"""

from datetime import datetime
from PyQt6.QtCore import QObject, pyqtSignal


class Logger(QObject):
    """日志记录器"""
    
    log_signal = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        
    def info(self, message):
        """记录信息日志"""
        self.log_signal.emit(f"[INFO] {message}")
        
    def warning(self, message):
        """记录警告日志"""
        self.log_signal.emit(f"[WARNING] {message}")
        
    def error(self, message):
        """记录错误日志"""
        self.log_signal.emit(f"[ERROR] {message}")
        
    def debug(self, message):
        """记录调试日志"""
        self.log_signal.emit(f"[DEBUG] {message}")
        
    def get_timestamp(self):
        """获取时间戳"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
